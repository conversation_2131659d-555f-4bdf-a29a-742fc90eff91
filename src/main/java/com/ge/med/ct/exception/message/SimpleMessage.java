package com.ge.med.ct.exception.message;

/**
 * 简单消息实现类
 * 用于支持直接使用String类型的消息
 */
public class SimpleMessage implements Message {
    
    private final String message;
    private final String key;
    
    /**
     * 创建简单消息
     * 
     * @param message 消息内容
     */
    public SimpleMessage(String message) {
        this.message = message != null ? message : "";
        this.key = "simple.message";
    }
    
    /**
     * 创建带键的简单消息
     * 
     * @param key 消息键
     * @param message 消息内容
     */
    public SimpleMessage(String key, String message) {
        this.key = key != null ? key : "simple.message";
        this.message = message != null ? message : "";
    }
    
    @Override
    public String getKey() {
        return key;
    }
    
    @Override
    public String getDefaultMessage() {
        return message;
    }
    
    @Override
    public Message format(Object... args) {
        if (args == null || args.length == 0) {
            return this;
        }
        
        // 简单的参数替换
        String formattedMessage = message;
        for (int i = 0; i < args.length; i++) {
            String placeholder = "{" + i + "}";
            if (formattedMessage.contains(placeholder)) {
                formattedMessage = formattedMessage.replace(placeholder, String.valueOf(args[i]));
            }
        }
        
        return new SimpleMessage(key, formattedMessage);
    }
    
    @Override
    public String toStr() {
        return message;
    }
    
    @Override
    public String toString() {
        return message;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        SimpleMessage other = (SimpleMessage) obj;
        return key.equals(other.key) && message.equals(other.message);
    }
    
    @Override
    public int hashCode() {
        return key.hashCode() * 31 + message.hashCode();
    }
}
