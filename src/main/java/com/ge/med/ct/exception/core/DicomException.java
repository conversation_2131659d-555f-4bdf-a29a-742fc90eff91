package com.ge.med.ct.exception.core;

import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.message.DicomMessages;
import com.ge.med.ct.exception.message.Message;

import java.util.Map;

/**
 * DICOM异常类
 * 用于表示DICOM操作过程中的异常
 */
public class DicomException extends QAToolException {
    private static final long serialVersionUID = 1L;

    /**
     * 创建异常构建器
     *
     * @return 异常构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 创建数据异常构建器
     * 用于创建DICOM数据相关的异常
     *
     * @return 数据异常构建器
     */
    public static Builder dataBuilder() {
        Builder builder = new Builder();
        builder.errorCode(ErrorCode.DICOM_VALIDATION);
        builder.message(DicomMessages.TAG_INVALID);
        return builder;
    }

    /**
     * 创建DICOM数据异常
     *
     * @param message 异常消息
     * @return DICOM数据异常
     */
    public static DicomException createDataException(Message message) {
        return new DicomException(ErrorCode.DICOM_VALIDATION, message);
    }

    /**
     * 创建DICOM数据异常
     *
     * @param message 异常消息
     * @param cause   原因
     * @return DICOM数据异常
     */
    public static DicomException createDataException(Message message, Throwable cause) {
        return new DicomException(ErrorCode.DICOM_VALIDATION, message, cause);
    }

    /**
     * 创建DICOM数据异常
     *
     * @param message 消息
     * @param args    消息参数
     * @return DICOM数据异常
     */
    public static DicomException createDataException(Message message, Object... args) {
        return new DicomException(ErrorCode.DICOM_VALIDATION, message, args);
    }

    /**
     * 创建DICOM数据异常
     *
     * @param message 消息
     * @param cause   原因
     * @param args    消息参数
     * @return DICOM数据异常
     */
    public static DicomException createDataException(Message message, Throwable cause, Object... args) {
        return new DicomException(ErrorCode.DICOM_VALIDATION, message, cause, args);
    }

    /**
     * 创建DICOM异常
     *
     * @param message 异常消息
     */
    public DicomException(Message message) {
        super(ErrorCode.PROCESSING, message);
    }

    /**
     * 创建DICOM异常 (简化版，支持String消息)
     *
     * @param message 异常消息字符串
     */
    public DicomException(String message) {
        super(ErrorCode.PROCESSING, new SimpleMessage(message));
    }

    /**
     * 创建带原因的DICOM异常
     *
     * @param message 异常消息
     * @param cause   原因
     */
    public DicomException(Message message, Throwable cause) {
        super(ErrorCode.PROCESSING, message, cause);
    }

    /**
     * 创建带错误码的DICOM异常
     *
     * @param errorCode 错误码
     * @param message   异常消息
     */
    public DicomException(ErrorCode errorCode, Message message) {
        super(errorCode, message);
    }

    /**
     * 创建带错误码和原因的DICOM异常
     *
     * @param errorCode 错误码
     * @param message   异常消息
     * @param cause     原因
     */
    public DicomException(ErrorCode errorCode, Message message, Throwable cause) {
        super(errorCode, message, cause);
    }

    /**
     * 创建带消息的DICOM异常
     *
     * @param message 消息
     * @param args    消息参数
     */
    public DicomException(Message message, Object... args) {
        super(ErrorCode.PROCESSING, message, args);
    }

    /**
     * 创建带消息和原因的DICOM异常
     *
     * @param message 消息
     * @param cause   原因
     * @param args    消息参数
     */
    public DicomException(Message message, Throwable cause, Object... args) {
        super(ErrorCode.PROCESSING, message, cause, args);
    }

    /**
     * 创建带错误码和消息的DICOM异常
     *
     * @param errorCode 错误码
     * @param message   消息
     * @param args      消息参数
     */
    public DicomException(ErrorCode errorCode, Message message, Object... args) {
        super(errorCode, message, args);
    }

    /**
     * 创建带错误码、消息和原因的DICOM异常
     *
     * @param errorCode 错误码
     * @param message   消息
     * @param cause     原因
     * @param args      消息参数
     */
    public DicomException(ErrorCode errorCode, Message message, Throwable cause, Object... args) {
        super(errorCode, message, cause, args);
    }

    /**
     * DICOM异常构建器
     */
    public static class Builder extends QAToolException.Builder {
        /**
         * 创建DICOM异常构建器
         */
        public Builder() {
            super();
            errorCode(ErrorCode.PROCESSING);
            message(DicomMessages.PROCESSING_ERROR);
        }

        /**
         * 设置错误码
         *
         * @param errorCode 错误码
         * @return 构建器
         */
        public Builder errorCode(ErrorCode errorCode) {
            super.errorCode(errorCode);
            return this;
        }

        /**
         * 构建DICOM异常
         *
         * @return DICOM异常实例
         */
        @Override
        public DicomException build() {
            // 直接创建DicomException对象，而不是先创建QAToolException对象
            ErrorCode errorCode = this.errorCode;
            Message message = this.message;
            Throwable cause = this.cause;
            Object[] messageArgs = this.messageArgs;

            DicomException dicomException = new DicomException(
                    errorCode,
                    message,
                    cause,
                    messageArgs);

            // 添加上下文
            for (Map.Entry<String, Object> entry : this.context.entrySet()) {
                dicomException.addContext(entry.getKey(), entry.getValue());
            }

            return dicomException;
        }
    }
}
