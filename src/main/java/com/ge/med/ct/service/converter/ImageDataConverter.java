package com.ge.med.ct.service.converter;

import com.ge.med.ct.dcm.core.cfg.table.TableColumnManager;
import com.ge.med.ct.dicom2.model.DicomImage;
import com.ge.med.ct.dicom2.tag.DicomTagConstants;
import com.ge.med.ct.exception.core.QAToolException;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * 图像数据转换器
 * 负责将DicomImage对象的数据转换为表格显示所需的字符串格式
 */
public class ImageDataConverter implements DataConverter<DicomImage> {
    private static final Logger LOG = Logger.getLogger(ImageDataConverter.class.getName());
    private final TagValueFormatter tagValueFormatter;
    private final TableColumnManager tableColumnManager;

    public ImageDataConverter(TagValueFormatter tagValueFormatter, TableColumnManager tableColumnManager) {
        this.tagValueFormatter = tagValueFormatter;
        this.tableColumnManager = tableColumnManager;
    }

    @Override
    public String getTagValue(DicomImage image, String columnName, String tableType) throws QAToolException {
        LOG.fine("处理图像列: " + columnName + ", 表格类型: " + tableType);
        if (image == null) return "";

        // 获取标签ID
        String tagId = tableColumnManager.resolveTagId(tableType, columnName);

        // 处理特殊列
        if ("special".equals(tagId)) {
            // 根据列名判断具体的特殊列类型
            if ("ImagePosition".equals(columnName)) {
                LOG.fine("处理图像位置列: " + columnName);
                String result = processImagePosition(image);
                LOG.fine("图像位置处理结果: " + result);
                return result;
            } else if ("ImageCount".equals(columnName)) {
                // 如果有其他特殊列，可以在这里添加处理逻辑
                return "";
            }
            return "";
        }

        if (tagId.isEmpty()) {
            LOG.warning("列 " + columnName + " 未找到对应的标签ID");
            return "";
        }

        // 获取标签值
        String tagValue = image.getTagValue(tagId);

        // 格式化值
        return tagValueFormatter.format(tagId, tagValue);
    }

    /**
     * 处理图像位置信息
     * 将DICOM标签中的图像位置数据（通常是三个坐标值）转换为人类可读的格式
     * 格式化为: I/S (Superior/Inferior), R/L (Right/Left), A/P (Anterior/Posterior)
     *
     * @param image DICOM图像对象
     * @return 格式化的位置信息字符串
     */
    private String processImagePosition(DicomImage image) {
        // 获取图像位置标签ID
        String tagId = DicomTagConstants.Image.IMAGE_POSITION_PATIENT;
        String position = image.getTagValue(tagId);

        if (position == null || position.isEmpty()) {
            return "";
        }

        try {
            // DICOM位置数据通常使用\\分隔的三个数值
            String[] coords = position.split("\\\\");
            if (coords.length >= 3) {
                double x = Double.parseDouble(coords[0]);
                double y = Double.parseDouble(coords[1]);
                double z = Double.parseDouble(coords[2]);

                // 格式化为医学影像常用的坐标系统表示
                // I/S: Inferior/Superior (上下方向)
                // R/L: Right/Left (左右方向)
                // A/P: Anterior/Posterior (前后方向)
                return String.format("I/S %.2f R/L %.1f A/P %.1f", z, x, y);
            }
        } catch (Exception e) {
            LOG.log(Level.WARNING, "处理图像位置信息失败: " + position, e);
        }
        return "";
    }
}