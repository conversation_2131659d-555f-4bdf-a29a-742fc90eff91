package com.ge.med.ct.service;

import com.ge.med.ct.dcm.model.DicomExam;
import com.ge.med.ct.dcm.model.DicomImage;
import com.ge.med.ct.dcm.model.DicomSeries;
import com.ge.med.ct.dcm.table.TableDataConverter;
import com.ge.med.ct.dcm.table.TableDataConverter.TableType;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;

import java.util.List;
import java.util.Vector;

/**
 * TableDataEngine适配器
 * 提供向后兼容的API，内部使用新的TableDataConverter
 * 
 * @deprecated 建议直接使用 {@link TableDataConverter}
 */
@Deprecated
public class TableDataEngineAdapter {
    
    private final TableDataConverter converter;
    
    public TableDataEngineAdapter() {
        this.converter = new TableDataConverter();
    }
    
    /**
     * 转换检查数据为表格数据
     */
    @HandleException(errorCode = ErrorCode.DATA)
    public Vector<Vector<String>> convertExamData(List<DicomExam> exams) {
        return converter.convertToVector(exams, TableType.EXAM);
    }
    
    /**
     * 转换序列数据为表格数据
     */
    @HandleException(errorCode = ErrorCode.DATA)
    public Vector<Vector<String>> convertSeriesData(List<DicomSeries> seriesList) {
        return converter.convertToVector(seriesList, TableType.SERIES);
    }
    
    /**
     * 转换图像数据为表格数据
     */
    @HandleException(errorCode = ErrorCode.DATA)
    public Vector<Vector<String>> convertImageData(List<DicomImage> images) {
        return converter.convertToVector(images, TableType.IMAGE);
    }
    
    /**
     * 获取表格列名
     */
    public String[] getTableColumnNames(String tableType) {
        TableType type = parseTableType(tableType);
        return converter.getColumnNamesArray(type);
    }
    
    /**
     * 通用数据转换方法
     */
    @HandleException(errorCode = ErrorCode.DATA)
    @SuppressWarnings("unchecked")
    public <T> Vector<Vector<String>> convertData(List<T> dataList, String tableType) {
        if (dataList == null || dataList.isEmpty()) {
            return new Vector<>();
        }
        
        TableType type = parseTableType(tableType);
        return converter.convertToVector(dataList, type);
    }
    
    /**
     * 解析表格类型字符串
     */
    private TableType parseTableType(String tableType) {
        if (tableType == null) {
            return TableType.EXAM;
        }
        
        switch (tableType.toUpperCase()) {
            case "TABLE_EXAM":
            case "EXAM":
                return TableType.EXAM;
            case "TABLE_SERIES":
            case "SERIES":
                return TableType.SERIES;
            case "TABLE_IMAGE":
            case "IMAGE":
                return TableType.IMAGE;
            default:
                return TableType.EXAM;
        }
    }
}
