package com.ge.med.ct.service;

import com.ge.med.ct.dcm_se.core.cfg.table.TableColumnManager;
import com.ge.med.ct.dicom2.model.DicomExam;
import com.ge.med.ct.dicom2.model.DicomImage;
import com.ge.med.ct.dicom2.model.DicomSeries;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.QAToolException;
import com.ge.med.ct.service.converter.DataConverter;

import java.util.List;
import java.util.Map;
import java.util.Vector;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 表格数据引擎
 * 负责处理表格数据的转换和展示
 */
public class TableDataEngine {
    private final TableColumnManager tableColumnManager;
    private final Map<Class<?>, DataConverter<?>> dataConverters;

    /**
     * 默认构造函数，仅用于创建匿名子类
     * 注意：此构造函数仅在子类中使用，不应直接调用
     */
    protected TableDataEngine() {
        this.tableColumnManager = null;
        this.dataConverters = null;
    }

    public TableDataEngine(TableColumnManager tableColumnManager) throws QAToolException {
        this.tableColumnManager = tableColumnManager;
        this.dataConverters = initDataConverters();

        // 验证配置
        TableColumnManager.validateTableConfigOrThrow(tableColumnManager);
    }

    /**
     * 初始化数据转换器
     */

    private Map<Class<?>, DataConverter<?>> initDataConverters() {
        Map<Class<?>, DataConverter<?>> converters = new ConcurrentHashMap<>();
        converters.put(DicomExam.class, DataConverter.createExamConverter(tableColumnManager));
        converters.put(DicomSeries.class, DataConverter.createSeriesConverter(tableColumnManager));
        converters.put(DicomImage.class, DataConverter.createImageConverter(tableColumnManager));
        return converters;
    }

    /**
     * 转换检查数据为表格数据
     */
    @HandleException(errorCode = ErrorCode.DATA)
    public Vector<Vector<String>> convertExamData(List<DicomExam> exams) {
        return convertData(exams, TableColumnManager.TABLE_EXAM);
    }

    /**
     * 转换序列数据为表格数据
     */
    @HandleException(errorCode = ErrorCode.DATA)
    public Vector<Vector<String>> convertSeriesData(List<DicomSeries> seriesList) {
        return convertData(seriesList, TableColumnManager.TABLE_SERIES);
    }

    /**
     * 转换图像数据为表格数据
     */
    @HandleException(errorCode = ErrorCode.DATA)
    public Vector<Vector<String>> convertImageData(List<DicomImage> images) {
        return convertData(images, TableColumnManager.TABLE_IMAGE);
    }

    @HandleException(errorCode = ErrorCode.DATA)
    @SuppressWarnings("unchecked")
    public <T> Vector<Vector<String>> convertData(List<T> dataList, String tableType) {
        if (dataList == null || dataList.isEmpty()) {
            return new Vector<>();
        }

        Vector<Vector<String>> data = new Vector<>();
        String[] columns = getTableColumnNames(tableType);

        // 获取数据类型
        Class<?> dataClass = dataList.get(0).getClass();

        // 获取对应的转换器
        DataConverter<?> rawConverter = dataConverters.get(dataClass);
        if (rawConverter == null) {
            return data; // 没有找到合适的转换器，返回空数据
        }

        // 使用@SuppressWarnings注解抑制类型转换警告
        DataConverter<T> converter = (DataConverter<T>) rawConverter;

        // 转换数据
        for (T item : dataList) {
            Vector<String> row = new Vector<>();
            for (String column : columns) {
                row.add(converter.getTagValue(item, column, tableType));
            }
            data.add(row);
        }

        return data;
    }

    /**
     * 获取表格列名
     *
     * @param tableType 表格类型
     * @return 列名数组
     */
    public String[] getTableColumnNames(String tableType) {
        List<String> columns = tableColumnManager.getColumnNames(tableType);
        return columns.toArray(new String[0]);
    }

}
