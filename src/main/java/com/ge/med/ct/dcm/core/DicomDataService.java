package com.ge.med.ct.dcm.core;

/**
 * DICOM数据服务统一接口
 * 支持多种数据获取方式的主服务接口
 */
public interface DicomDataService {

    /**
     * 加载DICOM数据
     * @param source 数据源配?(FileSystemDataSource ?PesiDataSource)
     * @param callback 进度回调
     */
    void loadDicomData(DicomDataSource source, ProgressCallback callback);

    /**
     * 获取数据提供?     * @return 数据提供者实?     */
    DicomDataProvider getDataProvider();

    /**
     * 获取当前数据源类?     * @return 当前使用的数据源类型
     */
    DataSourceType getCurrentSourceType();

    /**
     * 刷新数据
     * 重新加载当前数据源的数据
     */
    void refreshData();

    /**
     * 获取数据统计信息
     * @return 数据统计信息
     */
    DicomDataStats getDataStats();

    /**
     * 检查服务是否已初始?     * @return 是否已初始化
     */
    boolean isInitialized();

    /**
     * 关闭服务
     * 释放所有资?     */
    void close();

    /**
     * 进度回调接口
     */
    interface ProgressCallback {
        /**
         * 进度更新回调
         * @param info 进度信息
         */
        void onProgress(ProgressInfo info);

        /**
         * 错误回调
         * @param error 错误信息
         */
        void onError(String error);

        /**
         * 完成回调
         */
        void onComplete();
    }

    /**
     * 进度信息
     */
    class ProgressInfo {
        private final String message;
        private final int percentage;
        private final long timestamp;

        public ProgressInfo(String message, int percentage) {
            this.message = message;
            this.percentage = percentage;
            this.timestamp = System.currentTimeMillis();
        }

        public String getMessage() {
            return message;
        }

        public int getPercentage() {
            return percentage;
        }

        public long getTimestamp() {
            return timestamp;
        }

        @Override
        public String toString() {
            return String.format("ProgressInfo{message='%s', percentage=%d%%}", message, percentage);
        }
    }
}

