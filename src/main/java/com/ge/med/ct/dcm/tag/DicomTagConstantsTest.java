package com.ge.med.ct.dcm.tag;

import org.dcm4che3.data.VR;

/**
 * DicomTagConstants测试类
 * 验证标签常量是否正常工作
 */
public class DicomTagConstantsTest {
    
    public static void main(String[] args) {
        try {
            testTagConstants();
            testTagMappings();
            testUtilityMethods();
            testTagInfo();
            System.out.println("✅ DicomTagConstants测试通过！");
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testTagConstants() {
        System.out.println("测试标签常量...");
        
        // 测试患者标签
        assert DicomTagConstants.Patient.PATIENT_ID.equals("(0010,0020)") : "患者ID标签错误";
        assert DicomTagConstants.Patient.PATIENT_NAME.equals("(0010,0010)") : "患者姓名标签错误";
        
        // 测试检查标签
        assert DicomTagConstants.Study.STUDY_INSTANCE_UID.equals("(0020,000D)") : "检查实例UID标签错误";
        assert DicomTagConstants.Study.STUDY_DATE.equals("(0008,0020)") : "检查日期标签错误";
        
        // 测试序列标签
        assert DicomTagConstants.Series.SERIES_INSTANCE_UID.equals("(0020,000E)") : "序列实例UID标签错误";
        assert DicomTagConstants.Series.MODALITY.equals("(0008,0060)") : "模态标签错误";
        
        // 测试图像标签
        assert DicomTagConstants.Image.SOP_INSTANCE_UID.equals("(0008,0018)") : "SOP实例UID标签错误";
        assert DicomTagConstants.Image.ROWS.equals("(0028,0010)") : "行数标签错误";
        assert DicomTagConstants.Image.COLUMNS.equals("(0028,0011)") : "列数标签错误";
        
        // 测试CT标签
        assert DicomTagConstants.CT.KVP.equals("(0018,0060)") : "KVP标签错误";
        assert DicomTagConstants.CT.CONVOLUTION_KERNEL.equals("(0018,1210)") : "卷积核标签错误";
        
        // 测试设备标签
        assert DicomTagConstants.Equipment.MANUFACTURER.equals("(0008,0070)") : "制造商标签错误";
        assert DicomTagConstants.Equipment.STATION_NAME.equals("(0008,1010)") : "设备名称标签错误";
        
        System.out.println("✓ 标签常量测试通过");
    }
    
    private static void testTagMappings() {
        System.out.println("\n测试标签映射...");
        
        // 测试标签名称映射
        String patientIdName = DicomTagConstants.getTagName(DicomTagConstants.Patient.PATIENT_ID);
        assert "Patient ID".equals(patientIdName) : "患者ID名称映射错误: " + patientIdName;
        
        String studyDateName = DicomTagConstants.getTagName(DicomTagConstants.Study.STUDY_DATE);
        assert "Study Date".equals(studyDateName) : "检查日期名称映射错误: " + studyDateName;
        
        String modalityName = DicomTagConstants.getTagName(DicomTagConstants.Series.MODALITY);
        assert "Modality".equals(modalityName) : "模态名称映射错误: " + modalityName;
        
        // 测试VR映射
        VR patientIdVR = DicomTagConstants.getTagVR(DicomTagConstants.Patient.PATIENT_ID);
        assert VR.LO.equals(patientIdVR) : "患者ID VR映射错误: " + patientIdVR;
        
        VR studyDateVR = DicomTagConstants.getTagVR(DicomTagConstants.Study.STUDY_DATE);
        assert VR.DA.equals(studyDateVR) : "检查日期VR映射错误: " + studyDateVR;
        
        VR rowsVR = DicomTagConstants.getTagVR(DicomTagConstants.Image.ROWS);
        assert VR.US.equals(rowsVR) : "行数VR映射错误: " + rowsVR;
        
        // 测试未知标签
        String unknownName = DicomTagConstants.getTagName("(FFFF,FFFF)");
        assert unknownName.startsWith("Tag-") : "未知标签名称处理错误: " + unknownName;
        
        VR unknownVR = DicomTagConstants.getTagVR("(FFFF,FFFF)");
        assert VR.UN.equals(unknownVR) : "未知标签VR处理错误: " + unknownVR;
        
        System.out.println("✓ 标签映射测试通过");
    }
    
    private static void testUtilityMethods() {
        System.out.println("\n测试工具方法...");
        
        // 测试必要标签检查
        assert DicomTagConstants.isEssentialTag(DicomTagConstants.Patient.PATIENT_ID) : "患者ID应为必要标签";
        assert DicomTagConstants.isEssentialTag(DicomTagConstants.Study.STUDY_INSTANCE_UID) : "检查实例UID应为必要标签";
        assert DicomTagConstants.isEssentialTag(DicomTagConstants.Image.SOP_INSTANCE_UID) : "SOP实例UID应为必要标签";
        assert !DicomTagConstants.isEssentialTag(DicomTagConstants.CT.KVP) : "KVP不应为必要标签";
        
        // 测试常用显示标签检查
        assert DicomTagConstants.isCommonDisplayTag(DicomTagConstants.Patient.PATIENT_NAME) : "患者姓名应为常用显示标签";
        assert DicomTagConstants.isCommonDisplayTag(DicomTagConstants.Series.SERIES_DESCRIPTION) : "序列描述应为常用显示标签";
        assert !DicomTagConstants.isCommonDisplayTag(DicomTagConstants.Equipment.MANUFACTURER) : "制造商不应为常用显示标签";
        
        // 测试获取所有标签ID
        assert !DicomTagConstants.getAllTagIds().isEmpty() : "标签ID集合不应为空";
        assert DicomTagConstants.getAllTagIds().contains(DicomTagConstants.Patient.PATIENT_ID) : "应包含患者ID标签";
        
        System.out.println("✓ 工具方法测试通过");
    }
    
    private static void testTagInfo() {
        System.out.println("\n测试TagInfo类...");
        
        // 测试TagInfo创建
        DicomTagConstants.TagInfo patientIdInfo = DicomTagConstants.getTagInfo(DicomTagConstants.Patient.PATIENT_ID);
        assert patientIdInfo != null : "TagInfo不应为null";
        assert DicomTagConstants.Patient.PATIENT_ID.equals(patientIdInfo.getTagId()) : "TagInfo标签ID错误";
        assert "Patient ID".equals(patientIdInfo.getName()) : "TagInfo名称错误";
        assert VR.LO.equals(patientIdInfo.getVr()) : "TagInfo VR错误";
        
        // 测试TagInfo toString
        String infoString = patientIdInfo.toString();
        assert infoString.contains("(0010,0020)") : "TagInfo toString应包含标签ID";
        assert infoString.contains("Patient ID") : "TagInfo toString应包含标签名称";
        assert infoString.contains("LO") : "TagInfo toString应包含VR";
        
        // 测试TagInfo equals
        DicomTagConstants.TagInfo patientIdInfo2 = DicomTagConstants.getTagInfo(DicomTagConstants.Patient.PATIENT_ID);
        assert patientIdInfo.equals(patientIdInfo2) : "相同标签的TagInfo应相等";
        
        DicomTagConstants.TagInfo patientNameInfo = DicomTagConstants.getTagInfo(DicomTagConstants.Patient.PATIENT_NAME);
        assert !patientIdInfo.equals(patientNameInfo) : "不同标签的TagInfo不应相等";
        
        // 测试TagInfo hashCode
        assert patientIdInfo.hashCode() == patientIdInfo2.hashCode() : "相同标签的TagInfo应有相同hashCode";
        
        System.out.println("✓ TagInfo类测试通过");
    }
}
