package com.ge.med.ct.dcm.storage;

import com.ge.med.ct.dcm.core.DicomDataProvider;
import com.ge.med.ct.dcm.model.DicomExam;
import com.ge.med.ct.dcm.model.DicomSeries;
import com.ge.med.ct.dcm.model.DicomImage;
import com.ge.med.ct.dcm.model.DicomTag;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.message.DicomMessages;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.logging.Logger;

/**
 * DICOM导出器(简化版)
 * 简化DicomStorageService，只保留导出功能
 */
public class DicomExporter {
    private static final Logger LOG = Logger.getLogger(DicomExporter.class.getName());

    private final ObjectMapper objectMapper;

    public DicomExporter() {
        this.objectMapper = new ObjectMapper();
        this.objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
        this.objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
    }

    /**
     * 导出DICOM数据为JSON格式
     */
    public String exportToJson(DicomDataProvider provider, String filename) throws DicomException {
        if (provider == null) {
            throw new DicomException(DicomMessages.MODEL_NULL);
        }

        if (filename == null || filename.trim().isEmpty()) {
            filename = generateDefaultFilename();
        }

        try {
            LOG.info("开始导出DICOM数据到JSON: " + filename);

            // 构建导出数据结构
            Map<String, Object> exportData = buildExportData(provider);

            // 写入JSON文件
            Path filePath = Paths.get(filename);
            if (filePath.getParent() != null) {
                Files.createDirectories(filePath.getParent());
            }

            try (FileWriter writer = new FileWriter(filePath.toFile())) {
                objectMapper.writeValue(writer, exportData);
            }

            LOG.info("DICOM数据导出完成: " + filename);
            return filePath.toAbsolutePath().toString();

        } catch (Exception e) {
            String error = "导出DICOM数据失败: " + e.getMessage();
            LOG.severe(error);
            throw new DicomException(error, e);
        }
    }

    /**
     * 导出DICOM数据为CSV格式
     */
    public String exportToCsv(DicomDataProvider provider, String filename, ExportLevel level) throws DicomException {
        if (provider == null) {
            throw new DicomException(DicomMessages.MODEL_NULL);
        }

        if (level == null) {
            level = ExportLevel.EXAM;
        }

        if (filename == null || filename.trim().isEmpty()) {
            filename = generateDefaultCsvFilename(level);
        }

        try {
            LOG.info("开始导出DICOM数据到CSV: " + filename);

            Path filePath = Paths.get(filename);
            if (filePath.getParent() != null) {
                Files.createDirectories(filePath.getParent());
            }

            try (PrintWriter writer = new PrintWriter(new FileWriter(filePath.toFile()))) {
                switch (level) {
                    case EXAM:
                        exportExamsToCsv(provider, writer);
                        break;
                    case SERIES:
                        exportSeriesToCsv(provider, writer);
                        break;
                    case IMAGE:
                        exportImagesToCsv(provider, writer);
                        break;
                }
            }

            LOG.info("DICOM数据CSV导出完成: " + filename);
            return filePath.toAbsolutePath().toString();

        } catch (Exception e) {
            String error = "导出DICOM数据到CSV失败: " + e.getMessage();
            LOG.severe(error);
            throw new DicomException(error, e);
        }
    }

    /**
     * 导出数据统计信息
     */
    public String exportStatistics(DicomDataProvider provider, String filename) throws DicomException {
        if (provider == null) {
            throw new DicomException(DicomMessages.MODEL_NULL);
        }

        if (filename == null || filename.trim().isEmpty()) {
            filename = generateDefaultStatsFilename();
        }

        try {
            LOG.info("开始导出DICOM统计信息: " + filename);

            Map<String, Object> stats = buildStatistics(provider);

            Path filePath = Paths.get(filename);
            if (filePath.getParent() != null) {
                Files.createDirectories(filePath.getParent());
            }

            try (FileWriter writer = new FileWriter(filePath.toFile())) {
                objectMapper.writeValue(writer, stats);
            }

            LOG.info("DICOM统计信息导出完成: " + filename);
            return filePath.toAbsolutePath().toString();

        } catch (Exception e) {
            String error = "导出DICOM统计信息失败: " + e.getMessage();
            LOG.severe(error);
            throw new DicomException(error, e);
        }
    }

    // === 私有方法 ===

    private Map<String, Object> buildExportData(DicomDataProvider provider) {
        Map<String, Object> data = new HashMap<>();

        // 添加元信息
        data.put("exportInfo", buildExportInfo(provider));

        // 添加检查数据
        List<Map<String, Object>> exams = new ArrayList<>();
        for (DicomExam exam : provider.getAllExams()) {
            exams.add(buildExamData(exam, provider));
        }
        data.put("exams", exams);

        return data;
    }

    private Map<String, Object> buildExportInfo(DicomDataProvider provider) {
        Map<String, Object> info = new HashMap<>();
        info.put("exportTime", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        info.put("dataSourceType", provider.getDataSourceType().toString());
        info.put("statistics", provider.getDataStats());
        return info;
    }

    private Map<String, Object> buildExamData(DicomExam exam, DicomDataProvider provider) {
        Map<String, Object> examData = new HashMap<>();

        // 基本信息
        examData.put("id", exam.getId());
        examData.put("patientID", exam.getPatientID());
        examData.put("patientName", exam.getPatientName());
        examData.put("studyInstanceUID", exam.getStudyInstanceUID());
        examData.put("studyDate", exam.getStudyDate());
        examData.put("studyTime", exam.getStudyTime());
        examData.put("studyDescription", exam.getStudyDescription());

        // 标签数据
        examData.put("tags", buildTagsData(exam.getTags()));

        // 序列数据
        List<Map<String, Object>> seriesList = new ArrayList<>();
        for (DicomSeries series : provider.getSeriesForExam(exam.getId())) {
            seriesList.add(buildSeriesData(series, provider));
        }
        examData.put("series", seriesList);

        return examData;
    }

    private Map<String, Object> buildSeriesData(DicomSeries series, DicomDataProvider provider) {
        Map<String, Object> seriesData = new HashMap<>();

        // 基本信息
        seriesData.put("id", series.getId());
        seriesData.put("seriesInstanceUID", series.getSeriesInstanceUID());
        seriesData.put("seriesNumber", series.getSeriesNumber());
        seriesData.put("seriesDescription", series.getSeriesDescription());
        seriesData.put("modality", series.getModality());
        seriesData.put("seriesDate", series.getSeriesDate());
        seriesData.put("seriesTime", series.getSeriesTime());

        // 标签数据
        seriesData.put("tags", buildTagsData(series.getTags()));

        // 图像数据
        List<Map<String, Object>> imagesList = new ArrayList<>();
        for (DicomImage image : provider.getImagesForSeries(series.getId())) {
            imagesList.add(buildImageData(image, provider));
        }
        seriesData.put("images", imagesList);

        return seriesData;
    }

    private Map<String, Object> buildImageData(DicomImage image, DicomDataProvider provider) {
        Map<String, Object> imageData = new HashMap<>();

        // 基本信息
        imageData.put("id", image.getId());
        imageData.put("sopInstanceUID", image.getSopInstanceUID());
        imageData.put("instanceNumber", image.getInstanceNumber());
        imageData.put("imageType", image.getImageType());
        imageData.put("rows", image.getRows());
        imageData.put("columns", image.getColumns());
        imageData.put("filePath", provider.getImageFilePath(image.getId()));

        // 标签数据
        imageData.put("tags", buildTagsData(image.getTags()));

        return imageData;
    }

    private Map<String, Object> buildTagsData(Map<String, DicomTag> tags) {
        Map<String, Object> tagsData = new HashMap<>();

        for (Map.Entry<String, DicomTag> entry : tags.entrySet()) {
            DicomTag tag = entry.getValue();
            Map<String, Object> tagData = new HashMap<>();
            tagData.put("name", tag.getName());
            tagData.put("value", tag.getValueAsString());
            tagData.put("vr", tag.getVr().toString());
            tagsData.put(entry.getKey(), tagData);
        }

        return tagsData;
    }

    private void exportExamsToCsv(DicomDataProvider provider, PrintWriter writer) {
        // CSV标题行
        writer.println("PatientID,PatientName,StudyInstanceUID,StudyDate,StudyTime,StudyDescription,SeriesCount");

        // 数据行
        for (DicomExam exam : provider.getAllExams()) {
            writer.printf("%s,%s,%s,%s,%s,%s,%d%n",
                    csvEscape(exam.getPatientID()),
                    csvEscape(exam.getPatientName()),
                    csvEscape(exam.getStudyInstanceUID()),
                    csvEscape(exam.getStudyDate()),
                    csvEscape(exam.getStudyTime()),
                    csvEscape(exam.getStudyDescription()),
                    exam.getSeriesCount());
        }
    }

    private void exportSeriesToCsv(DicomDataProvider provider, PrintWriter writer) {
        // CSV标题行
        writer.println("ExamID,SeriesInstanceUID,SeriesNumber,SeriesDescription,Modality,SeriesDate,SeriesTime,ImageCount");

        // 数据行
        for (DicomExam exam : provider.getAllExams()) {
            for (DicomSeries series : provider.getSeriesForExam(exam.getId())) {
                writer.printf("%s,%s,%s,%s,%s,%s,%s,%d%n",
                        csvEscape(exam.getId()),
                        csvEscape(series.getSeriesInstanceUID()),
                        csvEscape(series.getSeriesNumber()),
                        csvEscape(series.getSeriesDescription()),
                        csvEscape(series.getModality()),
                        csvEscape(series.getSeriesDate()),
                        csvEscape(series.getSeriesTime()),
                        series.getImageCount());
            }
        }
    }

    private void exportImagesToCsv(DicomDataProvider provider, PrintWriter writer) {
        // CSV标题行
        writer.println("ExamID,SeriesID,SopInstanceUID,InstanceNumber,ImageType,Rows,Columns,FilePath");

        // 数据行
        for (DicomExam exam : provider.getAllExams()) {
            for (DicomSeries series : provider.getSeriesForExam(exam.getId())) {
                for (DicomImage image : provider.getImagesForSeries(series.getId())) {
                    writer.printf("%s,%s,%s,%s,%s,%s,%s,%s%n",
                            csvEscape(exam.getId()),
                            csvEscape(series.getId()),
                            csvEscape(image.getSopInstanceUID()),
                            csvEscape(image.getInstanceNumber()),
                            csvEscape(image.getImageType()),
                            csvEscape(String.valueOf(image.getRows())),
                            csvEscape(String.valueOf(image.getColumns())),
                            csvEscape(provider.getImageFilePath(image.getId())));
                }
            }
        }
    }

    private Map<String, Object> buildStatistics(DicomDataProvider provider) {
        Map<String, Object> stats = new HashMap<>();

        // 基本统计
        stats.put("dataStats", provider.getDataStats());
        stats.put("exportTime", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

        // 详细统计
        Map<String, Integer> modalityCount = new HashMap<>();
        Map<String, Integer> dateCount = new HashMap<>();

        for (DicomExam exam : provider.getAllExams()) {
            String studyDate = exam.getStudyDate();
            if (studyDate != null && !studyDate.isEmpty()) {
                // Java 8兼容的merge操作
                Integer currentCount = dateCount.get(studyDate);
                dateCount.put(studyDate, currentCount == null ? 1 : currentCount + 1);
            }

            for (DicomSeries series : provider.getSeriesForExam(exam.getId())) {
                String modality = series.getModality();
                if (modality != null && !modality.isEmpty()) {
                    // Java 8兼容的merge操作
                    Integer currentCount = modalityCount.get(modality);
                    modalityCount.put(modality, currentCount == null ? 1 : currentCount + 1);
                }
            }
        }

        stats.put("modalityDistribution", modalityCount);
        stats.put("dateDistribution", dateCount);

        return stats;
    }

    private String csvEscape(String value) {
        if (value == null) {
            return "";
        }

        // 如果包含逗号、引号或换行符，需要用引号包围并转义内部引号
        if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }

        return value;
    }

    private String generateDefaultFilename() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return "dicom_export_" + timestamp + ".json";
    }

    private String generateDefaultCsvFilename(ExportLevel level) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return "dicom_" + level.name().toLowerCase() + "_" + timestamp + ".csv";
    }

    private String generateDefaultStatsFilename() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return "dicom_stats_" + timestamp + ".json";
    }

    /**
     * 导出级别枚举
     */
    public enum ExportLevel {
        EXAM("检查级别"),
        SERIES("序列级别"),
        IMAGE("图像级别");

        private final String displayName;

        ExportLevel(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }

        @Override
        public String toString() {
            return displayName;
        }
    }
}