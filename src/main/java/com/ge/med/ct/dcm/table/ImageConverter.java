package com.ge.med.ct.dcm.table;

import com.ge.med.ct.dcm.model.DicomImage;

import java.util.logging.Logger;

/**
 * 图像数据转换�? * 负责将DicomImage对象转换为表格显示格�? */
public class ImageConverter implements TableDataEngine.DataConverter<DicomImage> {
    private static final Logger LOG = Logger.getLogger(ImageConverter.class.getName());
    
    private final TagFormatter formatter;
    private final TableDataEngine.TableColumnConfig columnConfig;
    
    public ImageConverter(TagFormatter formatter, TableDataEngine.TableColumnConfig columnConfig) {
        this.formatter = formatter;
        this.columnConfig = columnConfig;
    }
    
    @Override
    public String getColumnValue(DicomImage image, String columnName, TableDataEngine.TableType tableType) {
        if (image == null) {
            return "";
        }
        
        try {
            // 处理特殊�?            if (columnConfig.isSpecialColumn(columnName)) {
                return getSpecialColumnValue(image, columnName);
            }
            
            // 获取标签ID
            String tagId = columnConfig.getTagId(columnName);
            if (tagId == null || tagId.isEmpty()) {
                LOG.fine("�?" + columnName + " 未找到对应的标签ID");
                return "";
            }
            
            // 获取标签�?            String tagValue = image.getTagValue(tagId);
            if (tagValue == null || tagValue.trim().isEmpty()) {
                return "";
            }
            
            // 根据列名进行特殊格式�?            return formatColumnValue(columnName, tagId, tagValue);
            
        } catch (Exception e) {
            LOG.warning(String.format("获取图像列值失�? %s - %s", columnName, e.getMessage()));
            return "";
        }
    }
    
    /**
     * 获取特殊列的�?     */
    private String getSpecialColumnValue(DicomImage image, String columnName) {
        switch (columnName) {
            case "ImagePosition":
                return formatImagePosition(image);
            default:
                return "";
        }
    }
    
    /**
     * 格式化图像位�?     */
    private String formatImagePosition(DicomImage image) {
        float[] position = image.getImagePosition();
        if (position != null && position.length >= 3) {
            return String.format("(%.1f, %.1f, %.1f)", position[0], position[1], position[2]);
        }
        
        // 尝试从标签获�?        String positionTag = image.getTagValue("(0020,0032)");
        if (positionTag != null && !positionTag.trim().isEmpty()) {
            return formatter.formatImagePosition(positionTag);
        }
        
        return "";
    }
    
    /**
     * 根据列名格式化�?     */
    private String formatColumnValue(String columnName, String tagId, String tagValue) {
        switch (columnName) {
            case "InstanceNumber":
                return formatter.formatInteger(tagValue);
                
            case "ImageType":
                return formatter.formatSafeText(tagValue);
                
            case "Rows":
            case "Columns":
                return formatter.formatInteger(tagValue);
                
            case "SliceLocation":
                return formatter.formatSliceLocation(tagValue);
                
            case "WindowCenter":
            case "WindowWidth":
                return formatter.formatWindowValue(tagValue);
                
            default:
                // 使用通用格式�?                return formatter.format(tagId, tagValue);
        }
    }
}
