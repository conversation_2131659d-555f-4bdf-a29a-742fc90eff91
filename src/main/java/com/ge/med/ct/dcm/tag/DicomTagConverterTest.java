package com.ge.med.ct.dcm.tag;

import com.ge.med.ct.dcm.model.DicomTag;
import com.ge.med.ct.exception.core.DicomException;
import org.dcm4che3.data.VR;

/**
 * DicomTagConverter测试类
 * 验证标签转换功能是否正常工作
 */
public class DicomTagConverterTest {
    
    public static void main(String[] args) {
        try {
            testBasicConversions();
            testDicomFormatting();
            testTagCreation();
            testValidation();
            testNormalization();
            System.out.println("✅ DicomTagConverter测试通过！");
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testBasicConversions() {
        System.out.println("测试基本类型转换...");
        
        // 测试字符串转换
        assert "123".equals(DicomTagConverter.getStringValue("123")) : "字符串转换错误";
        assert "123".equals(DicomTagConverter.getStringValue(123)) : "数字到字符串转换错误";
        assert "".equals(DicomTagConverter.getStringValue(null)) : "null到字符串转换错误";
        
        // 测试整数转换
        assert Integer.valueOf(123).equals(DicomTagConverter.getIntegerValue("123")) : "字符串到整数转换错误";
        assert Integer.valueOf(123).equals(DicomTagConverter.getIntegerValue(123)) : "整数转换错误";
        assert DicomTagConverter.getIntegerValue("abc") == null : "无效字符串应返回null";
        assert DicomTagConverter.getIntegerValue(null) == null : "null应返回null";
        
        // 测试浮点数转换
        assert Float.valueOf(123.45f).equals(DicomTagConverter.getFloatValue("123.45")) : "字符串到浮点数转换错误";
        assert Float.valueOf(123.45f).equals(DicomTagConverter.getFloatValue(123.45f)) : "浮点数转换错误";
        assert DicomTagConverter.getFloatValue("abc") == null : "无效字符串应返回null";
        
        // 测试双精度转换
        assert Double.valueOf(123.45).equals(DicomTagConverter.getDoubleValue("123.45")) : "字符串到双精度转换错误";
        assert Double.valueOf(123.45).equals(DicomTagConverter.getDoubleValue(123.45)) : "双精度转换错误";
        
        System.out.println("✓ 基本类型转换测试通过");
    }
    
    private static void testDicomFormatting() {
        System.out.println("\n测试DICOM格式化...");
        
        // 测试日期格式化
        String formattedDate = DicomTagConverter.formatDicomDate("20231201");
        assert "2023-12-01".equals(formattedDate) : "DICOM日期格式化错误: " + formattedDate;
        
        // 测试时间格式化
        String formattedTime = DicomTagConverter.formatDicomTime("120000");
        assert "12:00:00".equals(formattedTime) : "DICOM时间格式化错误: " + formattedTime;
        
        // 测试时间格式化（带小数秒）
        String formattedTimeWithFraction = DicomTagConverter.formatDicomTime("120000.123");
        assert "12:00:00".equals(formattedTimeWithFraction) : "带小数秒的DICOM时间格式化错误: " + formattedTimeWithFraction;
        
        // 测试数值格式化
        String formattedNumber = DicomTagConverter.formatNumber(123.456);
        assert "123.46".equals(formattedNumber) : "数值格式化错误: " + formattedNumber;
        
        // 测试患者姓名格式化
        String formattedName = DicomTagConverter.formatPatientName("Smith^John^Middle");
        assert "Smith, John Middle".equals(formattedName) : "患者姓名格式化错误: " + formattedName;
        
        // 测试年龄格式化
        assert "25 岁".equals(DicomTagConverter.formatAge("025Y")) : "年龄格式化错误";
        assert "30 天".equals(DicomTagConverter.formatAge("030D")) : "天数格式化错误";
        assert "12 周".equals(DicomTagConverter.formatAge("012W")) : "周数格式化错误";
        assert "6 月".equals(DicomTagConverter.formatAge("006M")) : "月数格式化错误";
        
        // 测试性别格式化
        assert "男".equals(DicomTagConverter.formatSex("M")) : "男性格式化错误";
        assert "女".equals(DicomTagConverter.formatSex("F")) : "女性格式化错误";
        assert "其他".equals(DicomTagConverter.formatSex("O")) : "其他性别格式化错误";
        
        // 测试模态格式化
        assert "CT".equals(DicomTagConverter.formatModality("CT")) : "CT模态格式化错误";
        assert "MR".equals(DicomTagConverter.formatModality("MR")) : "MR模态格式化错误";
        
        System.out.println("✓ DICOM格式化测试通过");
    }
    
    private static void testTagCreation() throws DicomException {
        System.out.println("\n测试标签创建...");
        
        // 创建患者ID标签
        DicomTag patientIdTag = DicomTagConverter.createTag(DicomTagConstants.Patient.PATIENT_ID, "PATIENT001");
        assert patientIdTag != null : "标签创建失败";
        assert DicomTagConstants.Patient.PATIENT_ID.equals(patientIdTag.getTagId()) : "标签ID错误";
        assert "PATIENT001".equals(patientIdTag.getValueAsString()) : "标签值错误";
        assert VR.LO.equals(patientIdTag.getVr()) : "标签VR错误";
        
        // 创建行数标签
        DicomTag rowsTag = DicomTagConverter.createTag(DicomTagConstants.Image.ROWS, 512);
        assert rowsTag != null : "行数标签创建失败";
        assert "512".equals(rowsTag.getValueAsString()) : "行数标签值错误";
        assert VR.US.equals(rowsTag.getVr()) : "行数标签VR错误";
        
        System.out.println("✓ 标签创建测试通过");
    }
    
    private static void testValidation() {
        System.out.println("\n测试值验证...");
        
        // 测试整数验证
        assert DicomTagConverter.isValidValue(DicomTagConstants.Image.ROWS, "512") : "有效整数应通过验证";
        assert DicomTagConverter.isValidValue(DicomTagConstants.Image.ROWS, 512) : "整数对象应通过验证";
        assert !DicomTagConverter.isValidValue(DicomTagConstants.Image.ROWS, "abc") : "无效整数应不通过验证";
        
        // 测试浮点数验证
        assert DicomTagConverter.isValidValue(DicomTagConstants.Image.PIXEL_SPACING, "1.5") : "有效浮点数应通过验证";
        assert DicomTagConverter.isValidValue(DicomTagConstants.Image.PIXEL_SPACING, 1.5) : "浮点数对象应通过验证";
        
        // 测试日期验证
        assert DicomTagConverter.isValidValue(DicomTagConstants.Study.STUDY_DATE, "20231201") : "有效日期应通过验证";
        assert !DicomTagConverter.isValidValue(DicomTagConstants.Study.STUDY_DATE, "2023-12-01") : "无效日期格式应不通过验证";
        
        // 测试时间验证
        assert DicomTagConverter.isValidValue(DicomTagConstants.Study.STUDY_TIME, "120000") : "有效时间应通过验证";
        assert DicomTagConverter.isValidValue(DicomTagConstants.Study.STUDY_TIME, "120000.123") : "带小数秒的时间应通过验证";
        
        // 测试年龄验证
        assert DicomTagConverter.isValidValue(DicomTagConstants.Patient.PATIENT_AGE, "025Y") : "有效年龄应通过验证";
        assert DicomTagConverter.isValidValue(DicomTagConstants.Patient.PATIENT_AGE, "030D") : "有效天数应通过验证";
        assert !DicomTagConverter.isValidValue(DicomTagConstants.Patient.PATIENT_AGE, "25") : "无效年龄格式应不通过验证";
        
        // 测试空值验证
        assert DicomTagConverter.isValidValue(DicomTagConstants.Patient.PATIENT_ID, null) : "null值应通过验证";
        assert DicomTagConverter.isValidValue(DicomTagConstants.Patient.PATIENT_ID, "") : "空字符串应通过验证";
        
        System.out.println("✓ 值验证测试通过");
    }
    
    private static void testNormalization() {
        System.out.println("\n测试值标准化...");
        
        // 测试整数标准化
        assert "512".equals(DicomTagConverter.normalizeValue(DicomTagConstants.Image.ROWS, "512")) : "整数标准化错误";
        assert "512".equals(DicomTagConverter.normalizeValue(DicomTagConstants.Image.ROWS, 512)) : "整数对象标准化错误";
        
        // 测试浮点数标准化
        assert "1.5".equals(DicomTagConverter.normalizeValue(DicomTagConstants.Image.PIXEL_SPACING, "1.5")) : "浮点数标准化错误";
        assert "1.5".equals(DicomTagConverter.normalizeValue(DicomTagConstants.Image.PIXEL_SPACING, 1.5)) : "浮点数对象标准化错误";
        
        // 测试字符串标准化（大写）
        assert "CT".equals(DicomTagConverter.normalizeValue(DicomTagConstants.Series.MODALITY, "ct")) : "模态标准化错误";
        assert "PATIENT001".equals(DicomTagConverter.normalizeValue(DicomTagConstants.Patient.PATIENT_ID, "patient001")) : "患者ID标准化错误";
        
        // 测试空值标准化
        assert "".equals(DicomTagConverter.normalizeValue(DicomTagConstants.Patient.PATIENT_ID, null)) : "null值标准化错误";
        assert "".equals(DicomTagConverter.normalizeValue(DicomTagConstants.Patient.PATIENT_ID, "")) : "空字符串标准化错误";
        
        System.out.println("✓ 值标准化测试通过");
    }
}
