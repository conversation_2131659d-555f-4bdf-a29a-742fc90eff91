package com.ge.med.ct.dcm.core;

import com.ge.med.ct.dcm.model.DicomFileModel;
import com.ge.med.ct.exception.core.DicomException;

import java.util.List;

/**
 * PESI数据提供? * 通过数据库查询获取DICOM文件的PESI路径
 */
public class PesiDataProvider extends AbstractDicomDataProvider {

    private PesiQueryService pesiQueryService;
    private SystemCommandService commandService;
    private PesiDataSource currentSource;

    public PesiDataProvider() {
        this.dataSourceType = DataSourceType.PESI;
    }

    /**
     * 从PESI数据库加载DICOM数据
     */
    public void loadFromPesi(PesiDataSource source, DicomDataService.ProgressCallback callback) {
        this.currentSource = source;

        try {
            if (callback != null) {
                callback.onProgress(new DicomDataService.ProgressInfo("初始化PESI查询服务...", 0));
            }

            // 初始化服?            initializeServices(source);

            if (callback != null) {
                callback.onProgress(new DicomDataService.ProgressInfo("检查PESI服务可用?..", 10));
            }

            // 检查服务可用?            if (!pesiQueryService.isServiceAvailable()) {
                throw new RuntimeException("PESI查询服务不可? " + source.getExecuteQueryScript());
            }

            // 清除现有数据
            clearData();

            if (callback != null) {
                callback.onProgress(new DicomDataService.ProgressInfo("开始PESI数据查询...", 20));
            }

            // 执行PESI查询
            loadPesiData(callback);

            LOG.info(String.format("PESI加载完成: %d个检? %d个序? %d个图?,
                    exams.size(), series.size(), images.size()));

        } catch (Exception e) {
            LOG.severe("PESI加载失败: " + e.getMessage());
            throw new RuntimeException("PESI加载失败", e);
        }
    }

    @Override
    protected String getDataSourceDescription() {
        if (currentSource != null) {
            return currentSource.getDescription();
        }
        return "PESI数据?;
    }

    @Override
    protected void performShutdown() {
        // PESI提供者的清理工作
        pesiQueryService = null;
        commandService = null;
        currentSource = null;
    }

    // === 私有方法 ===

    private void initializeServices(PesiDataSource source) {
        // 创建系统命令服务
        commandService = new SystemCommandServiceImpl(source);

        // 创建PESI查询服务
        pesiQueryService = new PesiQueryServiceImpl(commandService, source.getImagePoolPath());
    }

    private void loadPesiData(DicomDataService.ProgressCallback callback) throws DicomException {
        // 这里实现PESI数据查询逻辑
        // 为了演示，我们先实现一个基础版本

        if (callback != null) {
            callback.onProgress(new DicomDataService.ProgressInfo("执行默认PESI查询...", 30));
        }

        // 执行一个示例查?        String sql = buildDefaultQuery();
        List<PesiQueryService.PesiQueryResult> results = pesiQueryService.executeQuery(sql);

        if (callback != null) {
            callback.onProgress(new DicomDataService.ProgressInfo(
                    String.format("查询?%d 条记录，开始构建PESI路径...", results.size()), 50));
        }

        // 处理查询结果
        processPesiResults(results, callback);

        if (callback != null) {
            callback.onProgress(new DicomDataService.ProgressInfo("构建数据结构...", 90));
        }

        // 构建数据结构
        buildPesiDataStructure();

        if (callback != null) {
            callback.onProgress(new DicomDataService.ProgressInfo("PESI加载完成", 100));
        }
    }

    private String buildDefaultQuery() {
        // 构建默认的PESI查询SQL
        // 这里可以根据需要修改查询条?        return "select img.patient_id,img.exam_id,img.image_set_id,img.image_id,img.dcm_image_id " +
               "from v_image img,imageset series, exam study " +
               "where study.exam_id=series.exam_id " +
               "and series.image_set_id=img.image_set_id " +
               "limit 100;"; // 限制结果数量以避免过多数?    }

    private void processPesiResults(List<PesiQueryService.PesiQueryResult> results,
                                   DicomDataService.ProgressCallback callback) throws DicomException {

        int totalResults = results.size();
        int processedResults = 0;

        for (PesiQueryService.PesiQueryResult result : results) {
            try {
                processPesiResult(result);
                processedResults++;

                if (callback != null && processedResults % 10 == 0) {
                    int progress = 50 + (int) ((processedResults * 30.0) / totalResults);
                    callback.onProgress(new DicomDataService.ProgressInfo(
                            String.format("已处?%d/%d 条PESI记录", processedResults, totalResults), progress));
                }

            } catch (Exception e) {
                LOG.warning(String.format("处理PESI结果失败: %s - %s", result.toString(), e.getMessage()));
            }
        }
    }

    private void processPesiResult(PesiQueryService.PesiQueryResult result) throws DicomException {
        // 构建PESI路径
        String pesiPath = pesiQueryService.buildPesiPath(result);

        if (pesiPath == null || pesiPath.isEmpty()) {
            LOG.warning("无法构建PESI路径: " + result.toString());
            return;
        }

        // 验证路径是否存在
        if (!pesiQueryService.validatePesiPath(pesiPath)) {
            LOG.warning("PESI路径不存? " + pesiPath);
            return;
        }

        // 创建文件模型
        String fileId = generatePesiFileId(result);
        DicomFileModel model = new DicomFileModel(fileId);
        model.setFilePath(pesiPath);
        model.setFileName(extractFileName(pesiPath));
        model.setFileType("DICOM");

        // 添加PESI相关的标?        addPesiTags(model, result);

        addFileModel(model);
    }

    private String generatePesiFileId(PesiQueryService.PesiQueryResult result) {
        return String.format("pesi_%s_%s_%s_%s",
                result.getPatientId(), result.getExamId(),
                result.getImageSetId(), result.getImageId());
    }

    private String extractFileName(String pesiPath) {
        int lastSeparator = Math.max(pesiPath.lastIndexOf('/'), pesiPath.lastIndexOf('\\'));
        return lastSeparator >= 0 ? pesiPath.substring(lastSeparator + 1) : pesiPath;
    }

    private void addPesiTags(DicomFileModel model, PesiQueryService.PesiQueryResult result) throws DicomException {
        // 基于PESI查询结果添加基本的DICOM标签

        // 患者ID
        model.addTag("(0010,0020)", new com.ge.med.ct.dcm.model.DicomTag(
                "(0010,0020)", "Patient_" + result.getPatientId(), org.dcm4che3.data.VR.LO));

        // 检查实例UID
        model.addTag("(0020,000D)", new com.ge.med.ct.dcm.model.DicomTag(
                "(0020,000D)", "*******.5." + result.getExamId(), org.dcm4che3.data.VR.UI));

        // 序列实例UID
        model.addTag("(0020,000E)", new com.ge.med.ct.dcm.model.DicomTag(
                "(0020,000E)", "*******.5.6." + result.getImageSetId(), org.dcm4che3.data.VR.UI));

        // SOP实例UID
        model.addTag("(0008,0018)", new com.ge.med.ct.dcm.model.DicomTag(
                "(0008,0018)", "*******.5.6.7." + result.getImageId(), org.dcm4che3.data.VR.UI));

        // 实例编号
        model.addTag("(0020,0013)", new com.ge.med.ct.dcm.model.DicomTag(
                "(0020,0013)", result.getDcmImageId(), org.dcm4che3.data.VR.IS));
    }

    private void buildPesiDataStructure() {
        // 构建PESI数据的层次结?        LOG.info("构建PESI数据结构: " + fileModels.size() + " 个文件模?);

        for (DicomFileModel model : fileModels.values()) {
            try {
                buildImageFromPesiModel(model);
            } catch (Exception e) {
                LOG.warning("构建PESI图像失败: " + model.getId() + " - " + e.getMessage());
            }
        }
    }

    private void buildImageFromPesiModel(DicomFileModel model) throws DicomException {
        // 从PESI文件模型构建图像、序列、检?        // 逻辑与FileSystemDataProvider类似，但使用PESI路径

        String studyUID = model.getStudyInstanceUID();
        String seriesUID = model.getSeriesInstanceUID();
        String imageUID = model.getSopInstanceUID();

        if (studyUID == null || seriesUID == null || imageUID == null) {
            LOG.warning("PESI文件模型缺少必要的UID: " + model.getId());
            return;
        }

        // 创建或获取检查
        if (!exams.containsKey(studyUID)) {
            com.ge.med.ct.dcm.model.DicomExam exam = new com.ge.med.ct.dcm.model.DicomExam(studyUID);
            exam.setPatientID(model.getPatientID());
            exam.setStudyInstanceUID(studyUID);
            exams.put(studyUID, exam);
        }

        // 创建或获取序列
        if (!series.containsKey(seriesUID)) {
            com.ge.med.ct.dcm.model.DicomSeries series = new com.ge.med.ct.dcm.model.DicomSeries(seriesUID);
            series.setSeriesInstanceUID(seriesUID);
            this.series.put(seriesUID, series);

            // 建立检查到序列的关系
            examToSeries.computeIfAbsent(studyUID, k -> new java.util.ArrayList<>()).add(seriesUID);
        }

        // 使用现有的图像
        com.ge.med.ct.dcm.model.DicomImage image = model.getImage();
        if (image != null) {
            images.put(imageUID, image);
            imageToFilePath.put(imageUID, model.getFilePath()); // PESI路径

            // 建立序列到图像的关系
            seriesToImages.computeIfAbsent(seriesUID, k -> new java.util.ArrayList<>()).add(imageUID);
        }
    }
}

