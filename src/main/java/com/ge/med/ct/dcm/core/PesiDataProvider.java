package com.ge.med.ct.dcm.core;

import com.ge.med.ct.dcm.model.*;
import com.ge.med.ct.dcm.tag.DicomTagConstants;
import com.ge.med.ct.exception.core.DicomException;
import org.dcm4che3.data.VR;

import java.util.List;
import java.util.ArrayList;

/**
 * PESI数据提供者
 * 通过数据库查询获取DICOM文件的PESI路径
 */
public class PesiDataProvider extends AbstractDicomDataProvider {

    private PesiQueryService pesiQueryService;
    private SystemCommandService commandService;
    private PesiDataSource currentSource;

    public PesiDataProvider() {
        this.dataSourceType = DataSourceType.PESI;
    }

    /**
     * 从PESI数据库加载DICOM数据
     */
    public void loadFromPesi(PesiDataSource source, DicomDataService.ProgressCallback callback) {
        this.currentSource = source;

        try {
            if (callback != null) {
                callback.onProgress(new DicomDataService.ProgressInfo("初始化PESI查询服务...", 0));
            }

            // 初始化服务
            initializeServices(source);

            if (callback != null) {
                callback.onProgress(new DicomDataService.ProgressInfo("检查PESI服务可用性...", 10));
            }

            // 检查服务可用性
            if (!pesiQueryService.isServiceAvailable()) {
                throw new RuntimeException("PESI查询服务不可用: " + source.getExecuteQueryScript());
            }

            // 清除现有数据
            clearData();

            if (callback != null) {
                callback.onProgress(new DicomDataService.ProgressInfo("开始PESI数据查询...", 20));
            }

            // 执行PESI查询
            loadPesiData(callback);

            LOG.info(String.format("PESI加载完成: %d个检查, %d个序列, %d个图像",
                    exams.size(), series.size(), images.size()));

            if (callback != null) {
                callback.onComplete();
            }

        } catch (Exception e) {
            LOG.severe("PESI加载失败: " + e.getMessage());
            if (callback != null) {
                callback.onError("PESI加载失败: " + e.getMessage());
            }
            throw new RuntimeException("PESI加载失败", e);
        }
    }

    /**
     * 基于搜索条件查询PESI数据
     */
    public void loadFromPesiWithCriteria(PesiDataSource source, DicomSearchCriteria criteria,
                                        DicomDataService.ProgressCallback callback) {
        this.currentSource = source;

        try {
            if (callback != null) {
                callback.onProgress(new DicomDataService.ProgressInfo("初始化PESI查询服务...", 0));
            }

            // 初始化服务
            initializeServices(source);

            // 清除现有数据
            clearData();

            if (callback != null) {
                callback.onProgress(new DicomDataService.ProgressInfo("执行PESI条件查询...", 20));
            }

            // 执行条件查询
            loadPesiDataWithCriteria(criteria, callback);

            LOG.info(String.format("PESI条件查询完成: %d个检查, %d个序列, %d个图像",
                    exams.size(), series.size(), images.size()));

            if (callback != null) {
                callback.onComplete();
            }

        } catch (Exception e) {
            LOG.severe("PESI条件查询失败: " + e.getMessage());
            if (callback != null) {
                callback.onError("PESI条件查询失败: " + e.getMessage());
            }
            throw new RuntimeException("PESI条件查询失败", e);
        }
    }

    @Override
    public String getImageFilePath(String imageId) {
        // PESI提供者返回PESI路径
        return imageToFilePath.get(imageId);
    }

    @Override
    public DicomImage getImageByPesiPath(String pesiPath) {
        // 根据PESI路径查找图像
        for (DicomImage image : images.values()) {
            String imagePath = getImageFilePath(image.getId());
            if (pesiPath.equals(imagePath)) {
                return image;
            }
        }
        return null;
    }

    @Override
    protected String getDataSourceDescription() {
        if (currentSource != null) {
            return currentSource.getDescription();
        }
        return "PESI数据源";
    }

    @Override
    protected void performShutdown() {
        // PESI提供者的清理工作
        pesiQueryService = null;
        commandService = null;
        currentSource = null;
    }

    // === 私有方法 ===

    private void initializeServices(PesiDataSource source) {
        // 创建系统命令服务
        commandService = new SystemCommandServiceImpl(source);

        // 创建PESI查询服务
        pesiQueryService = new PesiQueryServiceImpl(commandService, source.getImagePoolPath());
    }

    private void loadPesiData(DicomDataService.ProgressCallback callback) throws DicomException {
        if (callback != null) {
            callback.onProgress(new DicomDataService.ProgressInfo("执行默认PESI查询...", 30));
        }

        // 执行一个示例查询
        String sql = buildDefaultQuery();
        List<PesiQueryResult> results = pesiQueryService.executeQuery(sql);

        if (callback != null) {
            callback.onProgress(new DicomDataService.ProgressInfo(
                    String.format("查询到 %d 条记录，开始构建PESI路径...", results.size()), 50));
        }

        // 处理查询结果
        processPesiResults(results, callback);

        if (callback != null) {
            callback.onProgress(new DicomDataService.ProgressInfo("构建数据结构...", 90));
        }

        // 构建数据结构
        buildPesiDataStructure();
    }

    private void loadPesiDataWithCriteria(DicomSearchCriteria criteria,
                                         DicomDataService.ProgressCallback callback) throws DicomException {

        if (criteria.getDbParams() != null) {
            // 使用数据库查询参数
            DicomSearchCriteria.DatabaseQueryParams dbParams = criteria.getDbParams();

            List<PesiPathInfo> pathInfos;
            if (dbParams.getCustomSqlWhere() != null) {
                // 自定义SQL查询
                String sql = buildCustomQuery(dbParams.getCustomSqlWhere());
                List<PesiQueryResult> results = pesiQueryService.executeQuery(sql);
                pathInfos = convertToPesiPathInfos(results);
            } else {
                // 标准查询
                pathInfos = pesiQueryService.queryPesiPaths(
                        dbParams.getPatientNameUnicode(),
                        dbParams.getSeriesDescription());
            }

            if (callback != null) {
                callback.onProgress(new DicomDataService.ProgressInfo(
                        String.format("找到 %d 个PESI路径", pathInfos.size()), 60));
            }

            // 处理PESI路径信息
            processPesiPathInfos(pathInfos, callback);
        } else {
            // 使用标准搜索条件
            loadPesiDataWithStandardCriteria(criteria, callback);
        }
    }

    private void loadPesiDataWithStandardCriteria(DicomSearchCriteria criteria,
                                                 DicomDataService.ProgressCallback callback) throws DicomException {
        // 构建基于标准条件的SQL查询
        String sql = buildQueryFromCriteria(criteria);
        List<PesiQueryResult> results = pesiQueryService.executeQuery(sql);

        if (callback != null) {
            callback.onProgress(new DicomDataService.ProgressInfo(
                    String.format("查询到 %d 条记录", results.size()), 50));
        }

        // 处理查询结果
        processPesiResults(results, callback);

        // 构建数据结构
        buildPesiDataStructure();
    }

    private String buildDefaultQuery() {
        // 构建默认的PESI查询SQL
        return "select img.patient_id,img.exam_id,img.image_set_id,img.image_id,img.dcm_image_id " +
               "from v_image img,imageset series, exam study " +
               "where study.exam_id=series.exam_id " +
               "and series.image_set_id=img.image_set_id " +
               "limit 100;"; // 限制结果数量以避免过多数据
    }

    private String buildCustomQuery(String customWhere) {
        return "select img.patient_id,img.exam_id,img.image_set_id,img.image_id,img.dcm_image_id " +
               "from v_image img,imageset series, exam study " +
               "where study.exam_id=series.exam_id " +
               "and series.image_set_id=img.image_set_id " +
               "and " + customWhere + " " +
               "limit 1000;";
    }

    private String buildQueryFromCriteria(DicomSearchCriteria criteria) {
        StringBuilder sql = new StringBuilder();
        sql.append("select img.patient_id,img.exam_id,img.image_set_id,img.image_id,img.dcm_image_id ");
        sql.append("from v_image img,imageset series, exam study ");
        sql.append("where study.exam_id=series.exam_id ");
        sql.append("and series.image_set_id=img.image_set_id ");

        // 添加搜索条件
        if (criteria.getPatientName() != null && !criteria.getPatientName().trim().isEmpty()) {
            sql.append("and study.patient_name_unicode='").append(escapeSql(criteria.getPatientName())).append("' ");
        }

        if (criteria.getPatientId() != null && !criteria.getPatientId().trim().isEmpty()) {
            sql.append("and study.patient_id='").append(escapeSql(criteria.getPatientId())).append("' ");
        }

        if (criteria.getStudyDate() != null && !criteria.getStudyDate().trim().isEmpty()) {
            sql.append("and study.study_date='").append(escapeSql(criteria.getStudyDate())).append("' ");
        }

        if (criteria.getSeriesDescription() != null && !criteria.getSeriesDescription().trim().isEmpty()) {
            sql.append("and series.series_description='").append(escapeSql(criteria.getSeriesDescription())).append("' ");
        }

        if (criteria.getModality() != null && !criteria.getModality().trim().isEmpty()) {
            sql.append("and series.modality='").append(escapeSql(criteria.getModality())).append("' ");
        }

        sql.append("order by img.patient_id, img.exam_id, img.image_set_id, img.image_id ");
        sql.append("limit 1000;");

        return sql.toString();
    }

    private String escapeSql(String input) {
        return input != null ? input.replace("'", "''") : "";
    }

    private List<PesiPathInfo> convertToPesiPathInfos(List<PesiQueryResult> results) {
        List<PesiPathInfo> pathInfos = new ArrayList<>();

        for (PesiQueryResult result : results) {
            String pesiPath = pesiQueryService.buildPesiPath(result);
            if (pesiPath != null) {
                boolean exists = pesiQueryService.validatePesiPath(pesiPath);
                long fileSize = exists ? getFileSize(pesiPath) : 0L;
                pathInfos.add(new PesiPathInfo(pesiPath, result, exists, fileSize));
            }
        }

        return pathInfos;
    }

    private void processPesiResults(List<PesiQueryResult> results,
                                   DicomDataService.ProgressCallback callback) throws DicomException {
        int totalResults = results.size();
        int processedResults = 0;

        for (PesiQueryResult result : results) {
            try {
                // 构建PESI路径
                String pesiPath = pesiQueryService.buildPesiPath(result);
                if (pesiPath != null && pesiQueryService.validatePesiPath(pesiPath)) {
                    // 创建图像对象
                    DicomImage image = createImageFromPesiResult(result, pesiPath);
                    if (image != null) {
                        addImageToStructure(image, result);
                    }
                }

                processedResults++;
                if (callback != null && processedResults % 20 == 0) {
                    int progress = 50 + (int) ((processedResults * 30.0) / totalResults);
                    callback.onProgress(new DicomDataService.ProgressInfo(
                            String.format("已处理 %d/%d 个查询结果", processedResults, totalResults), progress));
                }

            } catch (Exception e) {
                LOG.warning(String.format("处理PESI查询结果失败: %s - %s", result.toString(), e.getMessage()));
            }
        }
    }

    private void processPesiPathInfos(List<PesiPathInfo> pathInfos,
                                     DicomDataService.ProgressCallback callback) throws DicomException {
        int totalPaths = pathInfos.size();
        int processedPaths = 0;

        for (PesiPathInfo pathInfo : pathInfos) {
            if (pathInfo.exists()) {
                try {
                    processPesiPathInfo(pathInfo);
                    processedPaths++;

                    if (callback != null && processedPaths % 10 == 0) {
                        int progress = 60 + (int) ((processedPaths * 25.0) / totalPaths);
                        callback.onProgress(new DicomDataService.ProgressInfo(
                                String.format("已处理 %d/%d 个PESI路径", processedPaths, totalPaths), progress));
                    }

                } catch (Exception e) {
                    LOG.warning(String.format("处理PESI路径失败: %s - %s", pathInfo.getPath(), e.getMessage()));
                }
            }
        }
    }

    private void processPesiPathInfo(PesiPathInfo pathInfo) throws DicomException {
        // 创建图像对象
        DicomImage image = createImageFromPesiResult(pathInfo.getQueryResult(), pathInfo.getPath());
        if (image != null) {
            addImageToStructure(image, pathInfo.getQueryResult());
        }
    }

    private DicomImage createImageFromPesiResult(PesiQueryResult result, String pesiPath) throws DicomException {
        String imageId = generateImageId(result);
        DicomImage image = new DicomImage(imageId);

        // 设置基本信息
        image.setSopInstanceUID(result.getDcmImageId());
        image.setInstanceNumber(result.getImageId());
        image.setFilePath(pesiPath);

        // 添加基本标签
        image.addTag(DicomTagConstants.Image.SOP_INSTANCE_UID,
                    new DicomTag(DicomTagConstants.Image.SOP_INSTANCE_UID, result.getDcmImageId(), VR.UI));
        image.addTag(DicomTagConstants.Image.INSTANCE_NUMBER,
                    new DicomTag(DicomTagConstants.Image.INSTANCE_NUMBER, result.getImageId(), VR.IS));

        // 记录文件路径映射
        imageToFilePath.put(imageId, pesiPath);

        return image;
    }

    private void addImageToStructure(DicomImage image, PesiQueryResult result) throws DicomException {
        String imageId = image.getId();
        String seriesId = generateSeriesId(result);
        String examId = generateExamId(result);

        // 添加图像
        images.put(imageId, image);

        // 确保序列存在
        if (!series.containsKey(seriesId)) {
            DicomSeries newSeries = createSeriesFromPesiResult(result, seriesId);
            series.put(seriesId, newSeries);
        }

        // 确保检查存在
        if (!exams.containsKey(examId)) {
            DicomExam newExam = createExamFromPesiResult(result, examId);
            exams.put(examId, newExam);
        }

        // 建立关联关系
        addToRelationship(seriesToImages, seriesId, imageId);
        addToRelationship(examToSeries, examId, seriesId);
    }

    private DicomSeries createSeriesFromPesiResult(PesiQueryResult result, String seriesId) throws DicomException {
        DicomSeries newSeries = new DicomSeries(seriesId);

        // 设置基本信息
        newSeries.setSeriesInstanceUID(result.getImageSetId());
        newSeries.setSeriesNumber(result.getImageSetId());

        // 添加基本标签
        newSeries.addTag(DicomTagConstants.Series.SERIES_INSTANCE_UID,
                        new DicomTag(DicomTagConstants.Series.SERIES_INSTANCE_UID, result.getImageSetId(), VR.UI));
        newSeries.addTag(DicomTagConstants.Series.SERIES_NUMBER,
                        new DicomTag(DicomTagConstants.Series.SERIES_NUMBER, result.getImageSetId(), VR.IS));

        return newSeries;
    }

    private DicomExam createExamFromPesiResult(PesiQueryResult result, String examId) throws DicomException {
        DicomExam newExam = new DicomExam(examId);

        // 设置基本信息
        newExam.setStudyInstanceUID(result.getExamId());

        // 添加基本标签
        newExam.addTag(DicomTagConstants.Study.STUDY_INSTANCE_UID,
                      new DicomTag(DicomTagConstants.Study.STUDY_INSTANCE_UID, result.getExamId(), VR.UI));

        return newExam;
    }

    private void buildPesiDataStructure() {
        // 构建检查-序列-图像的层次结构
        for (String examId : exams.keySet()) {
            DicomExam exam = exams.get(examId);
            List<String> seriesIds = examToSeries.get(examId);

            if (seriesIds != null) {
                for (String seriesId : seriesIds) {
                    DicomSeries series = this.series.get(seriesId);
                    if (series != null) {
                        exam.addSeries(series);

                        List<String> imageIds = seriesToImages.get(seriesId);
                        if (imageIds != null) {
                            for (String imageId : imageIds) {
                                DicomImage image = images.get(imageId);
                                if (image != null) {
                                    series.addImage(image);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private String generateImageId(PesiQueryResult result) {
        return String.format("IMG_%s_%s_%s_%s",
                result.getPatientId(), result.getExamId(),
                result.getImageSetId(), result.getImageId());
    }

    private String generateSeriesId(PesiQueryResult result) {
        return String.format("SER_%s_%s_%s",
                result.getPatientId(), result.getExamId(), result.getImageSetId());
    }

    private String generateExamId(PesiQueryResult result) {
        return String.format("EXM_%s_%s", result.getPatientId(), result.getExamId());
    }

    private long getFileSize(String filePath) {
        try {
            java.io.File file = new java.io.File(filePath);
            return file.exists() ? file.length() : 0L;
        } catch (Exception e) {
            return 0L;
        }
    }

    private void addToRelationship(java.util.Map<String, List<String>> relationMap, String key, String value) {
        relationMap.computeIfAbsent(key, k -> new ArrayList<>()).add(value);
    }
}