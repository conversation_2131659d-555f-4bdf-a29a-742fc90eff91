package com.ge.med.ct.dcm.core;

import java.util.List;

/**
 * PESI查询服务接口
 * 实现基于PESI路径查询指南的查询逻辑
 */
public interface PesiQueryService {
    
    /**
     * 根据患者信息查询PESI路径
     * @param patientName 患者姓名
     * @param seriesDescription 序列描述
     * @return PESI路径信息列表
     */
    List<PesiPathInfo> queryPesiPaths(String patientName, String seriesDescription);
    
    /**
     * 执行SQL查询
     * @param sql SQL查询语句
     * @return 查询结果列表
     */
    List<PesiQueryResult> executeQuery(String sql);
    
    /**
     * 根据数据库查询结果构建PESI路径
     * @param result 数据库查询结果
     * @return 实际的文件路径
     */
    String buildPesiPath(PesiQueryResult result);
    
    /**
     * 验证PESI路径是否存在
     * @param pesiPath PESI路径
     * @return 是否存在对应的文件
     */
    boolean validatePesiPath(String pesiPath);
    
    /**
     * 检查服务是否可用
     * @return executequery.sh是否可用
     */
    boolean isServiceAvailable();
}

/**
 * PESI查询结果
 */
class PesiQueryResult {
    private String patientId;
    private String examId;
    private String imageSetId;
    private String imageId;
    private String dcmImageId;
    
    public PesiQueryResult() {}
    
    public PesiQueryResult(String patientId, String examId, String imageSetId, String imageId, String dcmImageId) {
        this.patientId = patientId;
        this.examId = examId;
        this.imageSetId = imageSetId;
        this.imageId = imageId;
        this.dcmImageId = dcmImageId;
    }
    
    // Getter和Setter方法
    public String getPatientId() {
        return patientId;
    }
    
    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }
    
    public String getExamId() {
        return examId;
    }
    
    public void setExamId(String examId) {
        this.examId = examId;
    }
    
    public String getImageSetId() {
        return imageSetId;
    }
    
    public void setImageSetId(String imageSetId) {
        this.imageSetId = imageSetId;
    }
    
    public String getImageId() {
        return imageId;
    }
    
    public void setImageId(String imageId) {
        this.imageId = imageId;
    }
    
    public String getDcmImageId() {
        return dcmImageId;
    }
    
    public void setDcmImageId(String dcmImageId) {
        this.dcmImageId = dcmImageId;
    }
    
    @Override
    public String toString() {
        return String.format("PesiQueryResult{patientId='%s', examId='%s', imageSetId='%s', imageId='%s', dcmImageId='%s'}",
                patientId, examId, imageSetId, imageId, dcmImageId);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        PesiQueryResult other = (PesiQueryResult) obj;
        return safeEquals(patientId, other.patientId) &&
               safeEquals(examId, other.examId) &&
               safeEquals(imageSetId, other.imageSetId) &&
               safeEquals(imageId, other.imageId) &&
               safeEquals(dcmImageId, other.dcmImageId);
    }
    
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + safeHashCode(patientId);
        result = prime * result + safeHashCode(examId);
        result = prime * result + safeHashCode(imageSetId);
        result = prime * result + safeHashCode(imageId);
        result = prime * result + safeHashCode(dcmImageId);
        return result;
    }
    
    private boolean safeEquals(String a, String b) {
        return (a == null && b == null) || (a != null && a.equals(b));
    }
    
    private int safeHashCode(String s) {
        return s != null ? s.hashCode() : 0;
    }
}

/**
 * PESI路径信息
 */
class PesiPathInfo {
    private final String path;
    private final PesiQueryResult queryResult;
    private final boolean exists;
    private final long fileSize;
    
    public PesiPathInfo(String path, PesiQueryResult queryResult, boolean exists) {
        this(path, queryResult, exists, 0L);
    }
    
    public PesiPathInfo(String path, PesiQueryResult queryResult, boolean exists, long fileSize) {
        this.path = path;
        this.queryResult = queryResult;
        this.exists = exists;
        this.fileSize = fileSize;
    }
    
    public String getPath() {
        return path;
    }
    
    public PesiQueryResult getQueryResult() {
        return queryResult;
    }
    
    public boolean exists() {
        return exists;
    }
    
    public long getFileSize() {
        return fileSize;
    }
    
    @Override
    public String toString() {
        return String.format("PesiPathInfo{path='%s', exists=%s, fileSize=%d}",
                path, exists, fileSize);
    }
}
