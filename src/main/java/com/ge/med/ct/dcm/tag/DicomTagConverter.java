package com.ge.med.ct.dcm_se.tag;

import com.ge.med.ct.dcm_se.model.DicomTag;
import com.ge.med.ct.exception.core.DicomException;
import org.dcm4che3.data.VR;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Logger;

/**
 * DICOM标签转换器 (简化版)
 * 合并DicomTagService、DicomTagManager、DicomTagValueConverter的功能
 */
public final class DicomTagConverter {
    private static final Logger LOG = Logger.getLogger(DicomTagConverter.class.getName());
    
    // 日期格式化器
    private static final SimpleDateFormat DICOM_DATE_FORMAT = new SimpleDateFormat("yyyyMMdd");
    private static final SimpleDateFormat DICOM_TIME_FORMAT = new SimpleDateFormat("HHmmss");
    private static final SimpleDateFormat DISPLAY_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    private static final SimpleDateFormat DISPLAY_TIME_FORMAT = new SimpleDateFormat("HH:mm:ss");
    
    // 数值格式化器
    private static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat("#0.##");
    
    // 禁止实例化
    private DicomTagConverter() {
        throw new AssertionError("工具类不应被实例化");
    }
    
    // === 基本类型转换方法 ===
    
    /**
     * 转换为字符串值
     */
    public static String getStringValue(Object value) {
        if (value == null) {
            return "";
        }
        
        if (value instanceof String) {
            return (String) value;
        }
        
        if (value instanceof byte[]) {
            return new String((byte[]) value);
        }
        
        return value.toString();
    }
    
    /**
     * 转换为整数值
     */
    public static Integer getIntegerValue(Object value) {
        if (value == null) {
            return null;
        }
        
        if (value instanceof Integer) {
            return (Integer) value;
        }
        
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        
        String stringValue = getStringValue(value);
        if (stringValue.trim().isEmpty()) {
            return null;
        }
        
        try {
            return Integer.parseInt(stringValue.trim());
        } catch (NumberFormatException e) {
            LOG.fine("无法转换为整数: " + stringValue);
            return null;
        }
    }
    
    /**
     * 转换为浮点数值
     */
    public static Float getFloatValue(Object value) {
        if (value == null) {
            return null;
        }
        
        if (value instanceof Float) {
            return (Float) value;
        }
        
        if (value instanceof Number) {
            return ((Number) value).floatValue();
        }
        
        String stringValue = getStringValue(value);
        if (stringValue.trim().isEmpty()) {
            return null;
        }
        
        try {
            return Float.parseFloat(stringValue.trim());
        } catch (NumberFormatException e) {
            LOG.fine("无法转换为浮点数: " + stringValue);
            return null;
        }
    }
    
    /**
     * 转换为双精度浮点数值
     */
    public static Double getDoubleValue(Object value) {
        if (value == null) {
            return null;
        }
        
        if (value instanceof Double) {
            return (Double) value;
        }
        
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        
        String stringValue = getStringValue(value);
        if (stringValue.trim().isEmpty()) {
            return null;
        }
        
        try {
            return Double.parseDouble(stringValue.trim());
        } catch (NumberFormatException e) {
            LOG.fine("无法转换为双精度浮点数: " + stringValue);
            return null;
        }
    }
    
    // === DICOM特定格式转换 ===
    
    /**
     * 格式化DICOM日期为显示格式
     */
    public static String formatDicomDate(String dicomDate) {
        if (dicomDate == null || dicomDate.trim().isEmpty()) {
            return "";
        }
        
        try {
            Date date = DICOM_DATE_FORMAT.parse(dicomDate.trim());
            return DISPLAY_DATE_FORMAT.format(date);
        } catch (Exception e) {
            LOG.fine("无法解析DICOM日期: " + dicomDate);
            return dicomDate;
        }
    }
    
    /**
     * 格式化DICOM时间为显示格式
     */
    public static String formatDicomTime(String dicomTime) {
        if (dicomTime == null || dicomTime.trim().isEmpty()) {
            return "";
        }
        
        try {
            // DICOM时间可能包含小数秒，先处理
            String timeStr = dicomTime.trim();
            if (timeStr.contains(".")) {
                timeStr = timeStr.substring(0, timeStr.indexOf("."));
            }
            
            // 补齐到6位
            while (timeStr.length() < 6) {
                timeStr += "0";
            }
            
            Date time = DICOM_TIME_FORMAT.parse(timeStr);
            return DISPLAY_TIME_FORMAT.format(time);
        } catch (Exception e) {
            LOG.fine("无法解析DICOM时间: " + dicomTime);
            return dicomTime;
        }
    }
    
    /**
     * 格式化数值为显示格式
     */
    public static String formatNumber(Object value) {
        if (value == null) {
            return "";
        }
        
        if (value instanceof Number) {
            return DECIMAL_FORMAT.format(value);
        }
        
        Double doubleValue = getDoubleValue(value);
        if (doubleValue != null) {
            return DECIMAL_FORMAT.format(doubleValue);
        }
        
        return getStringValue(value);
    }
    
    /**
     * 格式化患者姓名
     */
    public static String formatPatientName(String patientName) {
        if (patientName == null || patientName.trim().isEmpty()) {
            return "";
        }
        
        // DICOM患者姓名格式: Family^Given^Middle^Prefix^Suffix
        String[] parts = patientName.split("\\^");
        StringBuilder formatted = new StringBuilder();
        
        // 姓
        if (parts.length > 0 && !parts[0].trim().isEmpty()) {
            formatted.append(parts[0].trim());
        }
        
        // 名
        if (parts.length > 1 && !parts[1].trim().isEmpty()) {
            if (formatted.length() > 0) {
                formatted.append(", ");
            }
            formatted.append(parts[1].trim());
        }
        
        // 中间名
        if (parts.length > 2 && !parts[2].trim().isEmpty()) {
            formatted.append(" ").append(parts[2].trim());
        }
        
        return formatted.toString();
    }
    
    /**
     * 格式化年龄
     */
    public static String formatAge(String age) {
        if (age == null || age.trim().isEmpty()) {
            return "";
        }
        
        String ageStr = age.trim();
        
        // DICOM年龄格式: nnnD, nnnW, nnnM, nnnY
        if (ageStr.matches("\\d+[DWMY]")) {
            char unit = ageStr.charAt(ageStr.length() - 1);
            String number = ageStr.substring(0, ageStr.length() - 1);
            
            switch (unit) {
                case 'D':
                    return number + " 天";
                case 'W':
                    return number + " 周";
                case 'M':
                    return number + " 月";
                case 'Y':
                    return number + " 岁";
                default:
                    return ageStr;
            }
        }
        
        return ageStr;
    }
    
    /**
     * 格式化性别
     */
    public static String formatSex(String sex) {
        if (sex == null || sex.trim().isEmpty()) {
            return "";
        }
        
        String sexStr = sex.trim().toUpperCase();
        switch (sexStr) {
            case "M":
                return "男";
            case "F":
                return "女";
            case "O":
                return "其他";
            default:
                return sexStr;
        }
    }
    
    /**
     * 格式化模态
     */
    public static String formatModality(String modality) {
        if (modality == null || modality.trim().isEmpty()) {
            return "";
        }
        
        String modalityStr = modality.trim().toUpperCase();
        switch (modalityStr) {
            case "CT":
                return "CT";
            case "MR":
                return "MR";
            case "CR":
                return "CR";
            case "DR":
                return "DR";
            case "US":
                return "US";
            case "XA":
                return "XA";
            case "RF":
                return "RF";
            case "MG":
                return "MG";
            case "DX":
                return "DX";
            default:
                return modalityStr;
        }
    }
    
    // === 标签创建和转换 ===
    
    /**
     * 创建DICOM标签
     */
    public static DicomTag createTag(String tagId, Object value) throws DicomException {
        if (tagId == null || tagId.trim().isEmpty()) {
            throw new DicomException("标签ID不能为空");
        }
        
        String stringValue = getStringValue(value);
        VR vr = DicomTagConstants.getTagVR(tagId);
        String name = DicomTagConstants.getTagName(tagId);
        
        return new DicomTag(tagId, name, stringValue, vr, null, false);
    }
    
    /**
     * 根据VR类型格式化标签值
     */
    public static String formatTagValue(String tagId, Object value) {
        if (value == null) {
            return "";
        }
        
        VR vr = DicomTagConstants.getTagVR(tagId);
        String stringValue = getStringValue(value);
        
        switch (vr) {
            case DA: // Date
                return formatDicomDate(stringValue);
            case TM: // Time
                return formatDicomTime(stringValue);
            case DS: // Decimal String
            case FL: // Floating Point Single
            case FD: // Floating Point Double
                return formatNumber(value);
            case PN: // Person Name
                return formatPatientName(stringValue);
            case AS: // Age String
                return formatAge(stringValue);
            case CS: // Code String
                if (DicomTagConstants.Patient.PATIENT_SEX.equals(tagId)) {
                    return formatSex(stringValue);
                } else if (DicomTagConstants.Series.MODALITY.equals(tagId)) {
                    return formatModality(stringValue);
                }
                return stringValue;
            default:
                return stringValue;
        }
    }
    
    /**
     * 获取标签的显示值
     */
    public static String getDisplayValue(DicomTag tag) {
        if (tag == null) {
            return "";
        }
        
        return formatTagValue(tag.getTagId(), tag.getValue());
    }
    
    /**
     * 检查标签值是否为空
     */
    public static boolean isEmpty(Object value) {
        if (value == null) {
            return true;
        }
        
        String stringValue = getStringValue(value);
        return stringValue.trim().isEmpty();
    }
    
    /**
     * 获取标签的简短描述
     */
    public static String getTagDescription(String tagId) {
        String name = DicomTagConstants.getTagName(tagId);
        VR vr = DicomTagConstants.getTagVR(tagId);
        return String.format("%s [%s] (%s)", name, tagId, vr);
    }
}
