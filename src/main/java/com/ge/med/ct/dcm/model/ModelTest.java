package com.ge.med.ct.dcm.model;

import com.ge.med.ct.exception.core.DicomException;
import org.dcm4che3.data.VR;

/**
 * 简单的模型测试类，验证循环引用问题是否已解决
 */
public class ModelTest {
    
    public static void main(String[] args) {
        try {
            testCircularReferenceFixed();
            System.out.println("✅ 循环引用问题已修复！");
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testCircularReferenceFixed() throws DicomException {
        // 1. 创建DICOM标签
        DicomTag patientIdTag = new DicomTag("(0010,0020)", "PATIENT001", VR.LO);
        DicomTag studyUidTag = new DicomTag("(0020,000D)", "1.2.3.4.5", VR.UI);
        DicomTag seriesUidTag = new DicomTag("(0020,000E)", "1.2.3.4.5.6", VR.UI);
        DicomTag sopUidTag = new DicomTag("(0008,0018)", "1.2.3.4.5.6.7", VR.UI);
        
        // 2. 创建检查
        DicomExam exam = new DicomExam("EXAM001");
        exam.addTag("(0010,0020)", patientIdTag);
        exam.addTag("(0020,000D)", studyUidTag);
        
        // 3. 创建序列
        DicomSeries series = new DicomSeries("SERIES001");
        series.addTag("(0020,000E)", seriesUidTag);
        
        // 4. 创建图像
        DicomImage image = new DicomImage("IMAGE001");
        image.addTag("(0008,0018)", sopUidTag);
        
        // 5. 创建文件模型
        DicomFileModel fileModel = new DicomFileModel("FILE001");
        fileModel.addTag("(0010,0020)", patientIdTag);
        fileModel.createImage(); // 延迟创建图像
        
        // 6. 建立关联关系（测试循环引用是否已修复）
        exam.addSeries(series);
        series.addImage(image);
        
        // 7. 验证关联关系
        assert exam.getSeries().contains(series) : "检查应包含序列";
        assert series.getExam() == exam : "序列应关联到检查";
        assert series.getImages().contains(image) : "序列应包含图像";
        assert image.getSeries() == series : "图像应关联到序列";
        
        // 8. 测试移除关联
        series.removeImage(image);
        assert !series.getImages().contains(image) : "序列不应包含已移除的图像";
        assert image.getSeries() == null : "图像不应关联到序列";
        
        exam.removeSeries(series);
        assert !exam.getSeries().contains(series) : "检查不应包含已移除的序列";
        assert series.getExam() == null : "序列不应关联到检查";
        
        // 9. 测试文件模型
        DicomImage fileImage = fileModel.getOrCreateImage();
        assert fileImage != null : "文件模型应有关联的图像";
        
        System.out.println("所有测试通过！");
    }
}
