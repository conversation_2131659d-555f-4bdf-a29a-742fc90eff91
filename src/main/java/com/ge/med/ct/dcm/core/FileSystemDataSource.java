package com.ge.med.ct.dcm.core;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件系统数据源配置
 * 用于扫描本地文件系统中的DICOM文件
 */
public class FileSystemDataSource implements DicomDataSource {
    
    private final String rootDirectory;
    private final boolean recursive;
    private final boolean enableMonitoring;
    private final String[] fileExtensions;
    private final int maxDepth;
    
    private FileSystemDataSource(Builder builder) {
        this.rootDirectory = builder.rootDirectory;
        this.recursive = builder.recursive;
        this.enableMonitoring = builder.enableMonitoring;
        this.fileExtensions = builder.fileExtensions;
        this.maxDepth = builder.maxDepth;
    }
    
    @Override
    public DataSourceType getType() {
        return DataSourceType.FILE_SYSTEM;
    }
    
    @Override
    public Map<String, Object> getConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("rootDirectory", rootDirectory);
        config.put("recursive", recursive);
        config.put("enableMonitoring", enableMonitoring);
        config.put("fileExtensions", fileExtensions);
        config.put("maxDepth", maxDepth);
        return config;
    }
    
    @Override
    public boolean isValid() {
        if (rootDirectory == null || rootDirectory.trim().isEmpty()) {
            return false;
        }
        
        File dir = new File(rootDirectory);
        return dir.exists() && dir.isDirectory() && dir.canRead();
    }
    
    @Override
    public String getDescription() {
        return String.format("文件系统扫描: %s (递归: %s, 监控: %s)", 
                rootDirectory, recursive ? "是" : "否", enableMonitoring ? "是" : "否");
    }
    
    // Getter方法
    public String getRootDirectory() {
        return rootDirectory;
    }
    
    public boolean isRecursive() {
        return recursive;
    }
    
    public boolean isEnableMonitoring() {
        return enableMonitoring;
    }
    
    public String[] getFileExtensions() {
        return fileExtensions != null ? fileExtensions.clone() : null;
    }
    
    public int getMaxDepth() {
        return maxDepth;
    }
    
    /**
     * Builder模式构建文件系统数据源
     */
    public static class Builder {
        private String rootDirectory;
        private boolean recursive = true;
        private boolean enableMonitoring = false;
        private String[] fileExtensions = {".dcm", ".dicom", ""};
        private int maxDepth = Integer.MAX_VALUE;
        
        public Builder rootDirectory(String rootDirectory) {
            this.rootDirectory = rootDirectory;
            return this;
        }
        
        public Builder recursive(boolean recursive) {
            this.recursive = recursive;
            return this;
        }
        
        public Builder enableMonitoring(boolean enableMonitoring) {
            this.enableMonitoring = enableMonitoring;
            return this;
        }
        
        public Builder fileExtensions(String... fileExtensions) {
            this.fileExtensions = fileExtensions;
            return this;
        }
        
        public Builder maxDepth(int maxDepth) {
            this.maxDepth = maxDepth;
            return this;
        }
        
        public FileSystemDataSource build() {
            if (rootDirectory == null || rootDirectory.trim().isEmpty()) {
                throw new IllegalArgumentException("根目录不能为空");
            }
            return new FileSystemDataSource(this);
        }
    }
    
    @Override
    public String toString() {
        return String.format("FileSystemDataSource{rootDirectory='%s', recursive=%s, enableMonitoring=%s}",
                rootDirectory, recursive, enableMonitoring);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        FileSystemDataSource other = (FileSystemDataSource) obj;
        return rootDirectory.equals(other.rootDirectory) &&
               recursive == other.recursive &&
               enableMonitoring == other.enableMonitoring;
    }
    
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + rootDirectory.hashCode();
        result = prime * result + (recursive ? 1231 : 1237);
        result = prime * result + (enableMonitoring ? 1231 : 1237);
        return result;
    }
}
