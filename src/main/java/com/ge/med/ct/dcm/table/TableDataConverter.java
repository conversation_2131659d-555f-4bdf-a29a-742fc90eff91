package com.ge.med.ct.dcm.table;

import com.ge.med.ct.dcm.model.*;
import com.ge.med.ct.dcm.tag.DicomTagConstants;

import java.util.*;
import java.util.function.Function;
import java.util.logging.Logger;

/**
 * 统一的表格数据转换器
 * 替代TableDataEngine和三个Converter类，提供简洁统一的表格数据转换功能
 */
public class TableDataConverter {
    private static final Logger LOG = Logger.getLogger(TableDataConverter.class.getName());

    private final TagFormatter formatter;
    private final Map<String, ColumnDefinition> columnDefinitions;

    public TableDataConverter() {
        this.formatter = new TagFormatter();
        this.columnDefinitions = initColumnDefinitions();
    }

    /**
     * 转换数据为表格格式
     */
    public <T> List<List<String>> convertToTable(List<T> dataList, TableType tableType) {
        if (dataList == null || dataList.isEmpty()) {
            return new ArrayList<>();
        }

        List<ColumnDefinition> columns = getColumnsForType(tableType);
        List<List<String>> result = new ArrayList<>();

        for (T item : dataList) {
            List<String> row = new ArrayList<>();
            for (ColumnDefinition column : columns) {
                String value = extractValue(item, column);
                row.add(value);
            }
            result.add(row);
        }

        return result;
    }

    /**
     * 获取表格列名
     */
    public List<String> getColumnNames(TableType tableType) {
        return getColumnsForType(tableType).stream()
                .map(ColumnDefinition::getName)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * 转换为Vector格式（兼容旧API）
     */
    public <T> Vector<Vector<String>> convertToVector(List<T> dataList, TableType tableType) {
        List<List<String>> table = convertToTable(dataList, tableType);
        Vector<Vector<String>> result = new Vector<>();

        for (List<String> row : table) {
            Vector<String> vectorRow = new Vector<>(row);
            result.add(vectorRow);
        }

        return result;
    }

    /**
     * 获取列名数组（兼容旧API）
     */
    public String[] getColumnNamesArray(TableType tableType) {
        List<String> names = getColumnNames(tableType);
        return names.toArray(new String[0]);
    }

    // === 私有方法 ===

    private String extractValue(Object item, ColumnDefinition column) {
        try {
            return column.getExtractor().apply(item);
        } catch (Exception e) {
            LOG.fine("提取列值失败: " + column.getName() + " - " + e.getMessage());
            return "";
        }
    }

    private List<ColumnDefinition> getColumnsForType(TableType tableType) {
        return columnDefinitions.values().stream()
                .filter(col -> col.getTableTypes().contains(tableType))
                .sorted(Comparator.comparing(ColumnDefinition::getOrder))
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    private Map<String, ColumnDefinition> initColumnDefinitions() {
        Map<String, ColumnDefinition> definitions = new HashMap<>();

        // === 患者信息列 ===
        addColumn(definitions, "PatientID", 1,
                EnumSet.of(TableType.EXAM),
                item -> formatter.formatSafeText(getExamTagValue(item, DicomTagConstants.Patient.PATIENT_ID)));

        addColumn(definitions, "PatientName", 2,
                EnumSet.of(TableType.EXAM),
                item -> formatter.formatPatientName(getExamTagValue(item, DicomTagConstants.Patient.PATIENT_NAME)));

        addColumn(definitions, "PatientSex", 3,
                EnumSet.of(TableType.EXAM),
                item -> formatter.formatSex(getExamTagValue(item, DicomTagConstants.Patient.PATIENT_SEX)));

        addColumn(definitions, "PatientAge", 4,
                EnumSet.of(TableType.EXAM),
                item -> formatter.formatAge(getExamTagValue(item, DicomTagConstants.Patient.PATIENT_AGE)));

        // === 检查信息列 ===
        addColumn(definitions, "StudyInstanceUID", 5,
                EnumSet.of(TableType.EXAM),
                item -> formatter.formatSafeText(getExamTagValue(item, DicomTagConstants.Study.STUDY_INSTANCE_UID)));

        addColumn(definitions, "StudyDate", 6,
                EnumSet.of(TableType.EXAM),
                item -> formatter.formatDate(getExamTagValue(item, DicomTagConstants.Study.STUDY_DATE)));

        addColumn(definitions, "StudyTime", 7,
                EnumSet.of(TableType.EXAM),
                item -> formatter.formatTime(getExamTagValue(item, DicomTagConstants.Study.STUDY_TIME)));

        addColumn(definitions, "StudyDescription", 8,
                EnumSet.of(TableType.EXAM),
                item -> formatter.formatSafeText(getExamTagValue(item, DicomTagConstants.Study.STUDY_DESCRIPTION)));

        addColumn(definitions, "SeriesCount", 9,
                EnumSet.of(TableType.EXAM),
                item -> String.valueOf(((DicomExam) item).getSeriesCount()));

        // === 序列信息列 ===
        addColumn(definitions, "SeriesInstanceUID", 1,
                EnumSet.of(TableType.SERIES),
                item -> formatter.formatSafeText(getSeriesTagValue(item, DicomTagConstants.Series.SERIES_INSTANCE_UID)));

        addColumn(definitions, "SeriesNumber", 2,
                EnumSet.of(TableType.SERIES),
                item -> formatter.formatInteger(getSeriesTagValue(item, DicomTagConstants.Series.SERIES_NUMBER)));

        addColumn(definitions, "SeriesDescription", 3,
                EnumSet.of(TableType.SERIES),
                item -> formatter.formatSafeText(getSeriesTagValue(item, DicomTagConstants.Series.SERIES_DESCRIPTION)));

        addColumn(definitions, "Modality", 4,
                EnumSet.of(TableType.SERIES),
                item -> formatter.formatModality(getSeriesTagValue(item, DicomTagConstants.Series.MODALITY)));

        addColumn(definitions, "SeriesDate", 5,
                EnumSet.of(TableType.SERIES),
                item -> formatter.formatDate(getSeriesTagValue(item, DicomTagConstants.Series.SERIES_DATE)));

        addColumn(definitions, "SeriesTime", 6,
                EnumSet.of(TableType.SERIES),
                item -> formatter.formatTime(getSeriesTagValue(item, DicomTagConstants.Series.SERIES_TIME)));

        addColumn(definitions, "ImageCount", 7,
                EnumSet.of(TableType.SERIES),
                item -> String.valueOf(((DicomSeries) item).getImageCount()));

        // === 图像信息列 ===
        addColumn(definitions, "SopInstanceUID", 1,
                EnumSet.of(TableType.IMAGE),
                item -> formatter.formatSafeText(getImageTagValue(item, DicomTagConstants.Image.SOP_INSTANCE_UID)));

        addColumn(definitions, "InstanceNumber", 2,
                EnumSet.of(TableType.IMAGE),
                item -> formatter.formatInteger(getImageTagValue(item, DicomTagConstants.Image.INSTANCE_NUMBER)));

        addColumn(definitions, "ImageType", 3,
                EnumSet.of(TableType.IMAGE),
                item -> formatter.formatSafeText(getImageTagValue(item, DicomTagConstants.Image.IMAGE_TYPE)));

        addColumn(definitions, "Rows", 4,
                EnumSet.of(TableType.IMAGE),
                item -> formatter.formatInteger(getImageTagValue(item, DicomTagConstants.Image.ROWS)));

        addColumn(definitions, "Columns", 5,
                EnumSet.of(TableType.IMAGE),
                item -> formatter.formatInteger(getImageTagValue(item, DicomTagConstants.Image.COLUMNS)));

        // === 图像位置和参数列 ===
        addColumn(definitions, "ImagePosition", 6,
                EnumSet.of(TableType.IMAGE),
                item -> {
                    DicomImage image = (DicomImage) item;
                    float[] position = image.getImagePosition();
                    if (position != null && position.length >= 3) {
                        return String.format("(%.1f, %.1f, %.1f)", position[0], position[1], position[2]);
                    }
                    String positionTag = image.getTagValue(DicomTagConstants.Image.IMAGE_POSITION_PATIENT);
                    return formatter.formatImagePosition(positionTag);
                });

        addColumn(definitions, "SliceLocation", 7,
                EnumSet.of(TableType.IMAGE),
                item -> formatter.formatSliceLocation(getImageTagValue(item, DicomTagConstants.Image.SLICE_LOCATION)));

        addColumn(definitions, "SliceThickness", 8,
                EnumSet.of(TableType.IMAGE),
                item -> formatter.formatSliceThickness(getImageTagValue(item, DicomTagConstants.Image.SLICE_THICKNESS)));

        addColumn(definitions, "PixelSpacing", 9,
                EnumSet.of(TableType.IMAGE),
                item -> formatter.formatPixelSpacing(getImageTagValue(item, DicomTagConstants.Image.PIXEL_SPACING)));

        addColumn(definitions, "WindowCenter", 10,
                EnumSet.of(TableType.IMAGE),
                item -> formatter.formatWindowValue(getImageTagValue(item, DicomTagConstants.Image.WINDOW_CENTER)));

        addColumn(definitions, "WindowWidth", 11,
                EnumSet.of(TableType.IMAGE),
                item -> formatter.formatWindowValue(getImageTagValue(item, DicomTagConstants.Image.WINDOW_WIDTH)));

        return definitions;
    }

    private void addColumn(Map<String, ColumnDefinition> definitions, String name, int order,
                          EnumSet<TableType> tableTypes, Function<Object, String> extractor) {
        definitions.put(name, new ColumnDefinition(name, order, tableTypes, extractor));
    }

    private String getExamTagValue(Object item, String tagId) {
        if (item instanceof DicomExam) {
            String value = ((DicomExam) item).getTagValue(tagId);
            return value != null ? value : "";
        }
        return "";
    }

    private String getSeriesTagValue(Object item, String tagId) {
        if (item instanceof DicomSeries) {
            String value = ((DicomSeries) item).getTagValue(tagId);
            return value != null ? value : "";
        }
        return "";
    }

    private String getImageTagValue(Object item, String tagId) {
        if (item instanceof DicomImage) {
            String value = ((DicomImage) item).getTagValue(tagId);
            return value != null ? value : "";
        }
        return "";
    }

    // === 枚举和内部类 ===

    /**
     * 表格类型枚举
     */
    public enum TableType {
        EXAM("检查"),
        SERIES("序列"),
        IMAGE("图像");

        private final String displayName;

        TableType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }

        @Override
        public String toString() {
            return displayName;
        }
    }

    /**
     * 列定义内部类
     */
    private static class ColumnDefinition {
        private final String name;
        private final int order;
        private final EnumSet<TableType> tableTypes;
        private final Function<Object, String> extractor;

        public ColumnDefinition(String name, int order, EnumSet<TableType> tableTypes,
                               Function<Object, String> extractor) {
            this.name = name;
            this.order = order;
            this.tableTypes = tableTypes;
            this.extractor = extractor;
        }

        public String getName() {
            return name;
        }

        public int getOrder() {
            return order;
        }

        public EnumSet<TableType> getTableTypes() {
            return tableTypes;
        }

        public Function<Object, String> getExtractor() {
            return extractor;
        }
    }
}
