package com.ge.med.ct.dcm.model;

import com.ge.med.ct.exception.core.DicomException;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * DICOM文件模型类(简化版)
 * 管理DICOM文件的属性和标签
 */
public class DicomFileModel {
    private static final Logger LOG = Logger.getLogger(DicomFileModel.class.getName());

    private final String id;
    private String filePath;
    private String fileName;
    private String fileType;
    private long fileSize;
    private DicomImage image;

    // 使用ConcurrentHashMap提高并发访问性能
    private final Map<String, DicomTag> tags;

    public DicomFileModel(String id) throws DicomException {
        if (id == null || id.trim().isEmpty()) {
            throw new DicomException("文件模型ID不能为空");
        }
        this.id = id;
        this.tags = new ConcurrentHashMap<>();
        try {
            this.image = new DicomImage(id);
        } catch (DicomException e) {
            LOG.warning("创建图像失败: " + e.getMessage());
            throw e;
        }
    }

    // === 基本属性 ===

    public String getId() {
        return id;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
        if (image != null) {
            image.setFilePath(filePath);
        }
        LOG.fine("设置文件路径: " + id + ": " + filePath);
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    // === 标签管理 ===

    public DicomTag getTag(String tagId) {
        return tags.get(tagId);
    }

    public void addTag(String tagId, DicomTag tag) {
        if (tagId != null && tag != null) {
            tags.put(tagId, tag);
            // 同步到图像对象
            if (image != null) {
                image.addTag(tagId, tag);
            }
            LOG.fine("添加标签 " + tagId + " 到文件模型 " + id);
        }
    }

    public Object getTagValue(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValueAsString() : null;
    }

    public String getTagValueAsString(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValueAsString() : null;
    }

    public Integer getTagValueAsInteger(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValueAsInteger() : null;
    }

    public Float getTagValueAsFloat(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValueAsFloat() : null;
    }

    public byte[] getTagBytes(String tagId) {
        DicomTag tag = getTag(tagId);
        if (tag != null && tag.getValueAsString() != null) {
            return tag.getValueAsString().getBytes();
        }
        return null;
    }

    public Map<String, DicomTag> getTags() {
        return Collections.unmodifiableMap(tags);
    }

    // === 常用DICOM属性快捷访问 ===

    public String getPatientID() {
        return getTagValueAsString("(0010,0020)");
    }

    public String getPatientName() {
        return getTagValueAsString("(0010,0010)");
    }

    public String getStudyInstanceUID() {
        return getTagValueAsString("(0020,000D)");
    }

    public String getSeriesInstanceUID() {
        return getTagValueAsString("(0020,000E)");
    }

    public String getSopInstanceUID() {
        return getTagValueAsString("(0008,0018)");
    }

    public String getStudyDate() {
        return getTagValueAsString("(0008,0020)");
    }

    public String getStudyTime() {
        return getTagValueAsString("(0008,0030)");
    }

    public String getModality() {
        return getTagValueAsString("(0008,0060)");
    }

    public String getSeriesDescription() {
        return getTagValueAsString("(0008,103E)");
    }

    public Integer getSeriesNumber() {
        return getTagValueAsInteger("(0020,0011)");
    }

    public Integer getInstanceNumber() {
        return getTagValueAsInteger("(0020,0013)");
    }

    public Integer getRows() {
        return getTagValueAsInteger("(0028,0010)");
    }

    public Integer getColumns() {
        return getTagValueAsInteger("(0028,0011)");
    }

    // === 图像对象 ===

    public DicomImage getImage() {
        return image;
    }

    public void setImage(DicomImage image) {
        this.image = image;
        if (image != null && filePath != null) {
            image.setFilePath(filePath);
        }
        LOG.fine("设置图像: " + id);
    }

    // === 工具方法 ===

    /**
     * 清除像素数据，释放内存
     */
    public void clearPixelData() {
        tags.remove("(7FE0,0010)"); // Pixel Data tag
        if (image != null) {
            image.clearPixelData();
        }
    }

    /**
     * 验证文件模型的基本完整性
     */
    public boolean isValid() {
        return id != null && !id.trim().isEmpty() &&
               filePath != null && !filePath.trim().isEmpty() &&
               getSopInstanceUID() != null;
    }

    /**
     * 获取文件的显示名称
     */
    public String getDisplayName() {
        if (fileName != null && !fileName.isEmpty()) {
            return fileName;
        }
        if (filePath != null) {
            int lastSeparator = Math.max(filePath.lastIndexOf('/'), filePath.lastIndexOf('\\'));
            return lastSeparator >= 0 ? filePath.substring(lastSeparator + 1) : filePath;
        }
        return id;
    }

    @Override
    public String toString() {
        return String.format("DicomFileModel[id=%s, filePath=%s, fileName=%s, fileSize=%d, fileType=%s, tags=%d]",
                id, filePath, fileName, fileSize, fileType, tags.size());
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        DicomFileModel other = (DicomFileModel) obj;
        return id.equals(other.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }
}

