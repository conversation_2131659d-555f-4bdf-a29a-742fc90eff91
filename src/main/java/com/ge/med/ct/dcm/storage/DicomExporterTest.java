package com.ge.med.ct.dcm.storage;

import com.ge.med.ct.dcm.core.DicomDataProvider;
import com.ge.med.ct.dcm.core.FileSystemDataProvider;
import com.ge.med.ct.dcm.model.DicomExam;
import com.ge.med.ct.dcm.model.DicomSeries;
import com.ge.med.ct.dcm.model.DicomImage;
import com.ge.med.ct.dcm.model.DicomTag;
import com.ge.med.ct.exception.core.DicomException;
import org.dcm4che3.data.VR;

import java.io.File;
import java.util.Arrays;

/**
 * DicomExporter测试类
 * 验证导出功能是否正常工作
 */
public class DicomExporterTest {
    
    public static void main(String[] args) {
        try {
            testDicomExporter();
            System.out.println("✅ DicomExporter测试通过！");
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testDicomExporter() throws DicomException {
        System.out.println("测试DicomExporter功能...");
        
        // 创建测试数据提供者
        DicomDataProvider provider = createTestDataProvider();
        
        // 创建导出器
        DicomExporter exporter = new DicomExporter();
        
        // 测试JSON导出
        String jsonFile = exporter.exportToJson(provider, "test_export.json");
        System.out.println("✓ JSON导出成功: " + jsonFile);
        
        // 测试CSV导出 - 检查级别
        String csvExamFile = exporter.exportToCsv(provider, "test_exams.csv", DicomExporter.ExportLevel.EXAM);
        System.out.println("✓ CSV检查导出成功: " + csvExamFile);
        
        // 测试CSV导出 - 序列级别
        String csvSeriesFile = exporter.exportToCsv(provider, "test_series.csv", DicomExporter.ExportLevel.SERIES);
        System.out.println("✓ CSV序列导出成功: " + csvSeriesFile);
        
        // 测试CSV导出 - 图像级别
        String csvImageFile = exporter.exportToCsv(provider, "test_images.csv", DicomExporter.ExportLevel.IMAGE);
        System.out.println("✓ CSV图像导出成功: " + csvImageFile);
        
        // 测试统计信息导出
        String statsFile = exporter.exportStatistics(provider, "test_stats.json");
        System.out.println("✓ 统计信息导出成功: " + statsFile);
        
        // 验证文件是否存在
        verifyFileExists(jsonFile);
        verifyFileExists(csvExamFile);
        verifyFileExists(csvSeriesFile);
        verifyFileExists(csvImageFile);
        verifyFileExists(statsFile);
        
        // 清理测试文件
        cleanupTestFiles(jsonFile, csvExamFile, csvSeriesFile, csvImageFile, statsFile);
        
        System.out.println("✓ 所有导出功能测试通过");
    }
    
    private static DicomDataProvider createTestDataProvider() throws DicomException {
        // 创建文件系统数据提供者（用于测试）
        FileSystemDataProvider provider = new FileSystemDataProvider();
        
        // 创建测试数据
        DicomExam exam = new DicomExam("EXAM001");
        exam.setPatientID("PATIENT001");
        exam.setPatientName("Test Patient");
        exam.setStudyInstanceUID("*******.5");
        exam.setStudyDate("20231201");
        exam.setStudyTime("120000");
        exam.setStudyDescription("Test Study");
        
        DicomSeries series = new DicomSeries("SERIES001");
        series.setSeriesInstanceUID("*******.5.6");
        series.setSeriesNumber("1");
        series.setSeriesDescription("Test Series");
        series.setModality("CT");
        series.setSeriesDate("20231201");
        series.setSeriesTime("120000");
        
        DicomImage image = new DicomImage("IMAGE001");
        image.setSopInstanceUID("*******.5.6.7");
        image.setInstanceNumber("1");
        image.setImageType("ORIGINAL\\PRIMARY\\AXIAL");
        image.setRows(512);
        image.setColumns(512);
        image.setFilePath("/test/path/image001.dcm");
        
        // 添加一些标签
        DicomTag patientIdTag = new DicomTag("(0010,0020)", "PATIENT001", VR.LO);
        DicomTag studyUidTag = new DicomTag("(0020,000D)", "*******.5", VR.UI);
        DicomTag seriesUidTag = new DicomTag("(0020,000E)", "*******.5.6", VR.UI);
        DicomTag sopUidTag = new DicomTag("(0008,0018)", "*******.5.6.7", VR.UI);
        
        exam.addTag("(0010,0020)", patientIdTag);
        exam.addTag("(0020,000D)", studyUidTag);
        series.addTag("(0020,000E)", seriesUidTag);
        image.addTag("(0008,0018)", sopUidTag);
        
        // 建立关联
        exam.addSeries(series);
        series.addImage(image);
        
        // 添加到数据提供者
        provider.addExam(exam);
        
        return provider;
    }
    
    private static void verifyFileExists(String filePath) {
        File file = new File(filePath);
        if (!file.exists()) {
            throw new RuntimeException("导出文件不存在: " + filePath);
        }
        if (file.length() == 0) {
            throw new RuntimeException("导出文件为空: " + filePath);
        }
        System.out.println("✓ 文件验证通过: " + filePath + " (大小: " + file.length() + " 字节)");
    }
    
    private static void cleanupTestFiles(String... files) {
        for (String filePath : files) {
            File file = new File(filePath);
            if (file.exists()) {
                if (file.delete()) {
                    System.out.println("✓ 清理测试文件: " + filePath);
                } else {
                    System.out.println("⚠ 无法删除测试文件: " + filePath);
                }
            }
        }
    }
}
