package com.ge.med.ct.dcm.model;

import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.message.DicomMessages;

import java.util.*;
import java.util.logging.Logger;

/**
 * DICOM序列类(简化版)
 * 管理DICOM序列属性和图像
 */
public class DicomSeries {
    private static final Logger LOG = Logger.getLogger(DicomSeries.class.getName());

    private final String id;
    private final Map<String, DicomTag> tags;
    private final List<DicomImage> images;
    private DicomExam exam;

    // 常用属性缓存
    private String seriesInstanceUID;
    private String seriesNumber;
    private String seriesDescription;
    private String modality;

    public DicomSeries(String id) throws DicomException {
        if (id == null || id.trim().isEmpty()) {
            throw new DicomException(DicomMessages.SERIES_ID_EMPTY);
        }
        this.id = id;
        this.tags = new HashMap<>();
        this.images = new ArrayList<>();
    }

    // === 基本属性访问 ===

    public String getId() {
        return id;
    }

    public DicomTag getTag(String tagId) {
        return tags.get(tagId);
    }

    public void addTag(String tagId, DicomTag tag) {
        if (tagId != null && tag != null) {
            tags.put(tagId, tag);
            updateCachedAttributes(tagId, tag.getValueAsString());
            LOG.fine("添加标签 " + tagId + " 到序列 " + id);
        }
    }

    public String getTagValue(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValueAsString() : null;
    }

    public Map<String, DicomTag> getTags() {
        return Collections.unmodifiableMap(tags);
    }

    // === 序列信息 ===

    public String getSeriesInstanceUID() {
        return seriesInstanceUID != null ? seriesInstanceUID : getTagValue("(0020,000E)");
    }

    public void setSeriesInstanceUID(String seriesInstanceUID) {
        this.seriesInstanceUID = seriesInstanceUID;
        setTagValue("(0020,000E)", seriesInstanceUID);
    }

    public String getSeriesNumber() {
        return seriesNumber != null ? seriesNumber : getTagValue("(0020,0011)");
    }

    public void setSeriesNumber(String seriesNumber) {
        this.seriesNumber = seriesNumber;
        setTagValue("(0020,0011)", seriesNumber);
    }

    public String getSeriesDescription() {
        return seriesDescription != null ? seriesDescription : getTagValue("(0008,103E)");
    }

    public void setSeriesDescription(String seriesDescription) {
        this.seriesDescription = seriesDescription;
        setTagValue("(0008,103E)", seriesDescription);
    }

    public String getModality() {
        return modality != null ? modality : getTagValue("(0008,0060)");
    }

    public void setModality(String modality) {
        this.modality = modality;
        setTagValue("(0008,0060)", modality);
    }

    public String getSeriesDate() {
        return getTagValue("(0008,0021)");
    }

    public void setSeriesDate(String seriesDate) {
        setTagValue("(0008,0021)", seriesDate);
    }

    public String getSeriesTime() {
        return getTagValue("(0008,0031)");
    }

    public void setSeriesTime(String seriesTime) {
        setTagValue("(0008,0031)", seriesTime);
    }

    // === 检查关联 ===

    public DicomExam getExam() {
        return exam;
    }

    public void setExam(DicomExam exam) {
        if (this.exam != exam) {
            DicomExam oldExam = this.exam;

            // 先设置本地引用
            this.exam = exam;

            // 清理旧关联 - 使用内部方法避免循环
            if (oldExam != null && oldExam.getSeries().contains(this)) {
                oldExam.internalRemoveSeries(this);
            }

            // 建立新关联 - 使用内部方法避免循环
            if (exam != null && !exam.getSeries().contains(this)) {
                exam.internalAddSeries(this);
            }
        }
    }

    /**
     * 内部方法：设置检查关联（避免循环引用）
     */
    void internalSetExam(DicomExam exam) {
        this.exam = exam;
    }

    // === 图像管理 ===

    public List<DicomImage> getImages() {
        return Collections.unmodifiableList(images);
    }

    public void addImage(DicomImage image) {
        if (image != null && !this.images.contains(image)) {
            this.images.add(image);
            // 设置反向关联，但避免循环调用
            if (image.getSeries() != this) {
                image.internalSetSeries(this);
            }
            LOG.fine("添加图像 " + image.getId() + " 到序列 " + id);
        }
    }

    /**
     * 内部方法：添加图像（避免循环引用）
     */
    void internalAddImage(DicomImage image) {
        if (image != null && !this.images.contains(image)) {
            this.images.add(image);
        }
    }

    public void removeImage(DicomImage image) {
        if (image != null && this.images.contains(image)) {
            this.images.remove(image);
            // 清除反向关联，但避免循环调用
            if (image.getSeries() == this) {
                image.internalSetSeries(null);
            }
            LOG.fine("从序列 " + id + " 移除图像 " + image.getId());
        }
    }

    /**
     * 内部方法：移除图像（避免循环引用）
     */
    void internalRemoveImage(DicomImage image) {
        if (image != null) {
            this.images.remove(image);
        }
    }

    public int getImageCount() {
        return images.size();
    }

    // === 工具方法 ===

    private void setTagValue(String tagId, String value) {
        if (value != null) {
            try {
                DicomTag tag = new DicomTag(tagId, value, org.dcm4che3.data.VR.LO);
                addTag(tagId, tag);
            } catch (DicomException e) {
                LOG.warning("设置标签值失败: " + tagId + " = " + value);
            }
        }
    }

    private void updateCachedAttributes(String tagId, String value) {
        switch (tagId) {
            case "(0020,000E)":
                this.seriesInstanceUID = value;
                break;
            case "(0020,0011)":
                this.seriesNumber = value;
                break;
            case "(0008,103E)":
                this.seriesDescription = value;
                break;
            case "(0008,0060)":
                this.modality = value;
                break;
        }
    }

    @Override
    public String toString() {
        return String.format("DicomSeries[id=%s, seriesInstanceUID=%s, seriesNumber=%s, description=%s, modality=%s, imageCount=%d]",
                id, getSeriesInstanceUID(), getSeriesNumber(), getSeriesDescription(), getModality(), images.size());
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        DicomSeries other = (DicomSeries) obj;
        return id.equals(other.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }
}
