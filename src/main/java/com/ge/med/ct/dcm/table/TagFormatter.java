package com.ge.med.ct.dcm.table;

import com.ge.med.ct.dcm.tag.DicomTagConverter;
import com.ge.med.ct.dcm.tag.DicomTagConstants;

import java.text.DecimalFormat;
import java.util.logging.Logger;

/**
 * 标签格式化器
 * 用于格式化DICOM标签值以便在表格中显示
 */
public class TagFormatter {
    private static final Logger LOG = Logger.getLogger(TagFormatter.class.getName());

    // 数值格式化器
    private static final DecimalFormat INTEGER_FORMAT = new DecimalFormat("#0");
    private static final DecimalFormat FLOAT_FORMAT = new DecimalFormat("#0.##");
    private static final DecimalFormat POSITION_FORMAT = new DecimalFormat("#0.0");
    private static final DecimalFormat PRECISION_FORMAT = new DecimalFormat("#0.000");

    /**
     * 格式化标签值
     */
    public String format(String tagId, Object value) {
        if (value == null) {
            return "";
        }

        // 使用DicomTagConverter进行基本格式化
        String formattedValue = DicomTagConverter.formatTagValue(tagId, value);

        // 针对表格显示进行额外的格式化
        return formatForTable(tagId, formattedValue);
    }

    /**
     * 针对表格显示的特殊格式化
     */
    private String formatForTable(String tagId, String value) {
        if (value == null || value.trim().isEmpty()) {
            return "";
        }

        // 根据标签类型进行特殊格式化
        if (DicomTagConstants.Image.IMAGE_POSITION_PATIENT.equals(tagId)) {
            return formatImagePosition(value);
        } else if (DicomTagConstants.Image.PIXEL_SPACING.equals(tagId)) {
            return formatPixelSpacing(value);
        } else if (DicomTagConstants.Image.WINDOW_CENTER.equals(tagId) ||
                   DicomTagConstants.Image.WINDOW_WIDTH.equals(tagId)) {
            return formatWindowValue(value);
        } else if (DicomTagConstants.Image.SLICE_THICKNESS.equals(tagId)) {
            return formatSliceThickness(value);
        } else if (DicomTagConstants.Image.SLICE_LOCATION.equals(tagId)) {
            return formatSliceLocation(value);
        } else if (DicomTagConstants.Image.IMAGE_ORIENTATION_PATIENT.equals(tagId)) {
            return formatImageOrientation(value);
        } else if (DicomTagConstants.Image.RESCALE_SLOPE.equals(tagId) ||
                   DicomTagConstants.Image.RESCALE_INTERCEPT.equals(tagId)) {
            return formatRescaleValue(value);
        } else if (DicomTagConstants.CT.KVP.equals(tagId)) {
            return formatKVP(value);
        } else if (DicomTagConstants.CT.EXPOSURE_TIME.equals(tagId)) {
            return formatExposureTime(value);
        } else if (DicomTagConstants.CT.X_RAY_TUBE_CURRENT.equals(tagId)) {
            return formatTubeCurrent(value);
        } else if (DicomTagConstants.CT.PITCH_FACTOR.equals(tagId)) {
            return formatPitchFactor(value);
        } else if (DicomTagConstants.CT.REVOLUTION_TIME.equals(tagId)) {
            return formatRevolutionTime(value);
        } else if (DicomTagConstants.Patient.PATIENT_WEIGHT.equals(tagId)) {
            return formatWeight(value);
        } else if (DicomTagConstants.Patient.PATIENT_SIZE.equals(tagId)) {
            return formatHeight(value);
        }

        return value;
    }

    /**
     * 格式化图像位置
     */
    public String formatImagePosition(String position) {
        if (position == null || position.trim().isEmpty()) {
            return "";
        }

        try {
            // DICOM图像位置格式: x\y\z
            String[] parts = position.split("\\\\");
            if (parts.length >= 3) {
                float x = Float.parseFloat(parts[0].trim());
                float y = Float.parseFloat(parts[1].trim());
                float z = Float.parseFloat(parts[2].trim());

                return String.format("(%.1f, %.1f, %.1f)", x, y, z);
            }
        } catch (NumberFormatException e) {
            LOG.fine("无法解析图像位置: " + position);
        }

        return position;
    }

    /**
     * 格式化图像方向
     */
    public String formatImageOrientation(String orientation) {
        if (orientation == null || orientation.trim().isEmpty()) {
            return "";
        }

        try {
            // DICOM图像方向格式: 6个值用\分隔
            String[] parts = orientation.split("\\\\");
            if (parts.length >= 6) {
                StringBuilder formatted = new StringBuilder();
                for (int i = 0; i < 6; i++) {
                    if (i > 0) formatted.append(", ");
                    float value = Float.parseFloat(parts[i].trim());
                    formatted.append(PRECISION_FORMAT.format(value));
                }
                return "[" + formatted.toString() + "]";
            }
        } catch (NumberFormatException e) {
            LOG.fine("无法解析图像方向: " + orientation);
        }

        return orientation;
    }

    /**
     * 格式化像素间距
     */
    public String formatPixelSpacing(String spacing) {
        if (spacing == null || spacing.trim().isEmpty()) {
            return "";
        }

        try {
            // DICOM像素间距格式: row\column
            String[] parts = spacing.split("\\\\");
            if (parts.length >= 2) {
                float row = Float.parseFloat(parts[0].trim());
                float col = Float.parseFloat(parts[1].trim());

                return String.format("%.2f x %.2f mm", row, col);
            } else if (parts.length == 1) {
                float value = Float.parseFloat(parts[0].trim());
                return FLOAT_FORMAT.format(value) + " mm";
            }
        } catch (NumberFormatException e) {
            LOG.fine("无法解析像素间距: " + spacing);
        }

        return spacing;
    }

    /**
     * 格式化窗位窗宽值
     */
    public String formatWindowValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return "";
        }

        try {
            // 可能包含多个值，用\分隔
            String[] parts = value.split("\\\\");
            if (parts.length > 0) {
                float firstValue = Float.parseFloat(parts[0].trim());
                return INTEGER_FORMAT.format(firstValue);
            }
        } catch (NumberFormatException e) {
            LOG.fine("无法解析窗位/窗宽值: " + value);
        }

        return value;
    }

    /**
     * 格式化层厚
     */
    public String formatSliceThickness(String thickness) {
        if (thickness == null || thickness.trim().isEmpty()) {
            return "";
        }

        try {
            float value = Float.parseFloat(thickness.trim());
            return FLOAT_FORMAT.format(value) + " mm";
        } catch (NumberFormatException e) {
            LOG.fine("无法解析层厚: " + thickness);
        }

        return thickness;
    }

    /**
     * 格式化层位置
     */
    public String formatSliceLocation(String location) {
        if (location == null || location.trim().isEmpty()) {
            return "";
        }

        try {
            float value = Float.parseFloat(location.trim());
            return POSITION_FORMAT.format(value) + " mm";
        } catch (NumberFormatException e) {
            LOG.fine("无法解析层位置: " + location);
        }

        return location;
    }

    /**
     * 格式化重建斜率/截距
     */
    public String formatRescaleValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return "";
        }

        try {
            float floatValue = Float.parseFloat(value.trim());
            return PRECISION_FORMAT.format(floatValue);
        } catch (NumberFormatException e) {
            LOG.fine("无法解析重建值: " + value);
        }

        return value;
    }

    /**
     * 格式化KVP值
     */
    public String formatKVP(String kvp) {
        if (kvp == null || kvp.trim().isEmpty()) {
            return "";
        }

        try {
            float value = Float.parseFloat(kvp.trim());
            return INTEGER_FORMAT.format(value) + " kV";
        } catch (NumberFormatException e) {
            LOG.fine("无法解析KVP值: " + kvp);
        }

        return kvp;
    }

    /**
     * 格式化曝光时间
     */
    public String formatExposureTime(String time) {
        if (time == null || time.trim().isEmpty()) {
            return "";
        }

        try {
            float value = Float.parseFloat(time.trim());
            return FLOAT_FORMAT.format(value) + " ms";
        } catch (NumberFormatException e) {
            LOG.fine("无法解析曝光时间: " + time);
        }

        return time;
    }

    /**
     * 格式化管电流
     */
    public String formatTubeCurrent(String current) {
        if (current == null || current.trim().isEmpty()) {
            return "";
        }

        try {
            float value = Float.parseFloat(current.trim());
            return INTEGER_FORMAT.format(value) + " mA";
        } catch (NumberFormatException e) {
            LOG.fine("无法解析管电流: " + current);
        }

        return current;
    }

    /**
     * 格式化螺距因子
     */
    public String formatPitchFactor(String pitch) {
        if (pitch == null || pitch.trim().isEmpty()) {
            return "";
        }

        try {
            float value = Float.parseFloat(pitch.trim());
            return FLOAT_FORMAT.format(value);
        } catch (NumberFormatException e) {
            LOG.fine("无法解析螺距因子: " + pitch);
        }

        return pitch;
    }

    /**
     * 格式化旋转时间
     */
    public String formatRevolutionTime(String time) {
        if (time == null || time.trim().isEmpty()) {
            return "";
        }

        try {
            float value = Float.parseFloat(time.trim());
            return FLOAT_FORMAT.format(value) + " s";
        } catch (NumberFormatException e) {
            LOG.fine("无法解析旋转时间: " + time);
        }

        return time;
    }

    /**
     * 格式化患者体重
     */
    public String formatWeight(String weight) {
        if (weight == null || weight.trim().isEmpty()) {
            return "";
        }

        try {
            float value = Float.parseFloat(weight.trim());
            return FLOAT_FORMAT.format(value) + " kg";
        } catch (NumberFormatException e) {
            LOG.fine("无法解析体重: " + weight);
        }

        return weight;
    }

    /**
     * 格式化患者身高
     */
    public String formatHeight(String height) {
        if (height == null || height.trim().isEmpty()) {
            return "";
        }

        try {
            float value = Float.parseFloat(height.trim());
            // DICOM身高通常以米为单位
            if (value < 3.0) {
                return FLOAT_FORMAT.format(value) + " m";
            } else {
                // 可能是厘米
                return FLOAT_FORMAT.format(value) + " cm";
            }
        } catch (NumberFormatException e) {
            LOG.fine("无法解析身高: " + height);
        }

        return height;
    }

    /**
     * 格式化整数值
     */
    public String formatInteger(Object value) {
        if (value == null) {
            return "";
        }

        Integer intValue = DicomTagConverter.getIntegerValue(value);
        if (intValue != null) {
            return INTEGER_FORMAT.format(intValue);
        }

        return DicomTagConverter.getStringValue(value);
    }

    /**
     * 格式化浮点数值
     */
    public String formatFloat(Object value) {
        if (value == null) {
            return "";
        }

        Float floatValue = DicomTagConverter.getFloatValue(value);
        if (floatValue != null) {
            return FLOAT_FORMAT.format(floatValue);
        }

        return DicomTagConverter.getStringValue(value);
    }

    /**
     * 格式化日期
     */
    public String formatDate(String date) {
        return DicomTagConverter.formatDicomDate(date);
    }

    /**
     * 格式化时间
     */
    public String formatTime(String time) {
        return DicomTagConverter.formatDicomTime(time);
    }

    /**
     * 格式化患者姓名
     */
    public String formatPatientName(String name) {
        return DicomTagConverter.formatPatientName(name);
    }

    /**
     * 格式化年龄
     */
    public String formatAge(String age) {
        return DicomTagConverter.formatAge(age);
    }

    /**
     * 格式化性别
     */
    public String formatSex(String sex) {
        return DicomTagConverter.formatSex(sex);
    }

    /**
     * 格式化模态
     */
    public String formatModality(String modality) {
        return DicomTagConverter.formatModality(modality);
    }

    /**
     * 格式化标签值用于表格显示（带单位和格式化）
     */
    public String formatForDisplay(String tagId, Object value) {
        if (value == null) {
            return "";
        }

        // 使用标签常量进行比较
        if (DicomTagConstants.Patient.PATIENT_ID.equals(tagId)) {
            return formatSafeText(DicomTagConverter.getStringValue(value));
        } else if (DicomTagConstants.Patient.PATIENT_NAME.equals(tagId)) {
            return formatPatientName(DicomTagConverter.getStringValue(value));
        } else if (DicomTagConstants.Patient.PATIENT_SEX.equals(tagId)) {
            return formatSex(DicomTagConverter.getStringValue(value));
        } else if (DicomTagConstants.Patient.PATIENT_AGE.equals(tagId)) {
            return formatAge(DicomTagConverter.getStringValue(value));
        } else if (DicomTagConstants.Patient.PATIENT_WEIGHT.equals(tagId)) {
            return formatWeight(DicomTagConverter.getStringValue(value));
        } else if (DicomTagConstants.Patient.PATIENT_SIZE.equals(tagId)) {
            return formatHeight(DicomTagConverter.getStringValue(value));
        } else if (DicomTagConstants.Study.STUDY_DATE.equals(tagId)) {
            return formatDate(DicomTagConverter.getStringValue(value));
        } else if (DicomTagConstants.Study.STUDY_TIME.equals(tagId)) {
            return formatTime(DicomTagConverter.getStringValue(value));
        } else if (DicomTagConstants.Series.MODALITY.equals(tagId)) {
            return formatModality(DicomTagConverter.getStringValue(value));
        } else if (DicomTagConstants.Image.ROWS.equals(tagId) ||
                   DicomTagConstants.Image.COLUMNS.equals(tagId)) {
            return formatInteger(value);
        } else {
            return format(tagId, value);
        }
    }

    /**
     * 截断长文本
     */
    public String truncateText(String text, int maxLength) {
        if (text == null || text.length() <= maxLength) {
            return text;
        }

        return text.substring(0, maxLength - 3) + "...";
    }

    /**
     * 格式化为表格显示的安全文本
     */
    public String formatSafeText(String text) {
        if (text == null) {
            return "";
        }

        // 移除换行符和制表符
        String safeText = text.replaceAll("[\r\n\t]", " ");

        // 限制长度
        return truncateText(safeText.trim(), 50);
    }

    /**
     * 格式化数值范围（如窗位窗宽的多个值）
     */
    public String formatValueRange(String values) {
        if (values == null || values.trim().isEmpty()) {
            return "";
        }

        try {
            String[] parts = values.split("\\\\");
            if (parts.length > 1) {
                StringBuilder formatted = new StringBuilder();
                for (int i = 0; i < Math.min(parts.length, 3); i++) { // 最多显示3个值
                    if (i > 0) formatted.append(", ");
                    float value = Float.parseFloat(parts[i].trim());
                    formatted.append(INTEGER_FORMAT.format(value));
                }
                if (parts.length > 3) {
                    formatted.append("...");
                }
                return formatted.toString();
            } else {
                float value = Float.parseFloat(values.trim());
                return INTEGER_FORMAT.format(value);
            }
        } catch (NumberFormatException e) {
            LOG.fine("无法解析数值范围: " + values);
        }

        return values;
    }

    /**
     * 检查标签是否需要特殊格式化
     */
    public boolean needsSpecialFormatting(String tagId) {
        return DicomTagConstants.Image.IMAGE_POSITION_PATIENT.equals(tagId) ||
               DicomTagConstants.Image.IMAGE_ORIENTATION_PATIENT.equals(tagId) ||
               DicomTagConstants.Image.PIXEL_SPACING.equals(tagId) ||
               DicomTagConstants.Image.WINDOW_CENTER.equals(tagId) ||
               DicomTagConstants.Image.WINDOW_WIDTH.equals(tagId) ||
               DicomTagConstants.Image.SLICE_THICKNESS.equals(tagId) ||
               DicomTagConstants.Image.SLICE_LOCATION.equals(tagId) ||
               DicomTagConstants.CT.KVP.equals(tagId) ||
               DicomTagConstants.CT.EXPOSURE_TIME.equals(tagId) ||
               DicomTagConstants.CT.X_RAY_TUBE_CURRENT.equals(tagId) ||
               DicomTagConstants.Patient.PATIENT_WEIGHT.equals(tagId) ||
               DicomTagConstants.Patient.PATIENT_SIZE.equals(tagId);
    }
}