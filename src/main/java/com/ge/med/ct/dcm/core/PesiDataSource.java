package com.ge.med.ct.dcm.core;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * PESI数据源配置
 * 用于通过数据库查询获取DICOM文件的PESI路径
 */
public class PesiDataSource implements DicomDataSource {
    
    private final String executeQueryScript;
    private final String imagePoolPath;
    private final String connectionConfig;
    private final int queryTimeout;
    private final boolean enableCache;
    
    private PesiDataSource(Builder builder) {
        this.executeQueryScript = builder.executeQueryScript;
        this.imagePoolPath = builder.imagePoolPath;
        this.connectionConfig = builder.connectionConfig;
        this.queryTimeout = builder.queryTimeout;
        this.enableCache = builder.enableCache;
    }
    
    @Override
    public DataSourceType getType() {
        return DataSourceType.PESI;
    }
    
    @Override
    public Map<String, Object> getConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("executeQueryScript", executeQueryScript);
        config.put("imagePoolPath", imagePoolPath);
        config.put("connectionConfig", connectionConfig);
        config.put("queryTimeout", queryTimeout);
        config.put("enableCache", enableCache);
        return config;
    }
    
    @Override
    public boolean isValid() {
        // 检查executequery.sh脚本是否存在
        if (executeQueryScript == null || executeQueryScript.trim().isEmpty()) {
            return false;
        }
        
        File scriptFile = new File(executeQueryScript);
        if (!scriptFile.exists() || !scriptFile.canExecute()) {
            return false;
        }
        
        // 检查图像池路径是否存在
        if (imagePoolPath == null || imagePoolPath.trim().isEmpty()) {
            return false;
        }
        
        File poolDir = new File(imagePoolPath);
        return poolDir.exists() && poolDir.isDirectory() && poolDir.canRead();
    }
    
    @Override
    public String getDescription() {
        return String.format("PESI查询: %s -> %s (超时: %ds, 缓存: %s)", 
                executeQueryScript, imagePoolPath, queryTimeout, enableCache ? "是" : "否");
    }
    
    // Getter方法
    public String getExecuteQueryScript() {
        return executeQueryScript;
    }
    
    public String getImagePoolPath() {
        return imagePoolPath;
    }
    
    public String getConnectionConfig() {
        return connectionConfig;
    }
    
    public int getQueryTimeout() {
        return queryTimeout;
    }
    
    public boolean isEnableCache() {
        return enableCache;
    }
    
    /**
     * Builder模式构建PESI数据源
     */
    public static class Builder {
        private String executeQueryScript;
        private String imagePoolPath;
        private String connectionConfig;
        private int queryTimeout = 30; // 默认30秒超时
        private boolean enableCache = true;
        
        public Builder executeQueryScript(String executeQueryScript) {
            this.executeQueryScript = executeQueryScript;
            return this;
        }
        
        public Builder imagePoolPath(String imagePoolPath) {
            this.imagePoolPath = imagePoolPath;
            return this;
        }
        
        public Builder connectionConfig(String connectionConfig) {
            this.connectionConfig = connectionConfig;
            return this;
        }
        
        public Builder queryTimeout(int queryTimeout) {
            this.queryTimeout = queryTimeout;
            return this;
        }
        
        public Builder enableCache(boolean enableCache) {
            this.enableCache = enableCache;
            return this;
        }
        
        public PesiDataSource build() {
            if (executeQueryScript == null || executeQueryScript.trim().isEmpty()) {
                throw new IllegalArgumentException("executeQuery脚本路径不能为空");
            }
            if (imagePoolPath == null || imagePoolPath.trim().isEmpty()) {
                throw new IllegalArgumentException("图像池路径不能为空");
            }
            return new PesiDataSource(this);
        }
    }
    
    /**
     * 创建默认的PESI数据源
     * 使用标准的路径配置
     */
    public static PesiDataSource createDefault() {
        return new Builder()
                .executeQueryScript("/usr/local/bin/executequery.sh")
                .imagePoolPath("/usr/g/sdc_image_pool/images")
                .queryTimeout(30)
                .enableCache(true)
                .build();
    }
    
    /**
     * 创建用于测试的PESI数据源
     */
    public static PesiDataSource createForTesting(String scriptPath, String poolPath) {
        return new Builder()
                .executeQueryScript(scriptPath)
                .imagePoolPath(poolPath)
                .queryTimeout(10)
                .enableCache(false)
                .build();
    }
    
    @Override
    public String toString() {
        return String.format("PesiDataSource{executeQueryScript='%s', imagePoolPath='%s', queryTimeout=%d, enableCache=%s}",
                executeQueryScript, imagePoolPath, queryTimeout, enableCache);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        PesiDataSource other = (PesiDataSource) obj;
        return executeQueryScript.equals(other.executeQueryScript) &&
               imagePoolPath.equals(other.imagePoolPath) &&
               queryTimeout == other.queryTimeout &&
               enableCache == other.enableCache;
    }
    
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + executeQueryScript.hashCode();
        result = prime * result + imagePoolPath.hashCode();
        result = prime * result + queryTimeout;
        result = prime * result + (enableCache ? 1231 : 1237);
        return result;
    }
}
