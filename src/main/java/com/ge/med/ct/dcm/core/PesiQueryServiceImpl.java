package com.ge.med.ct.dcm.core;

import java.io.File;
import java.util.*;
import java.util.logging.Logger;

/**
 * PESI查询服务实现
 * 实现基于PESI路径查询指南的查询逻辑
 */
public class PesiQueryServiceImpl implements PesiQueryService {
    private static final Logger LOG = Logger.getLogger(PesiQueryServiceImpl.class.getName());

    private final SystemCommandService commandService;
    private final String imagePoolPath;

    public PesiQueryServiceImpl(SystemCommandService commandService, String imagePoolPath) {
        this.commandService = commandService;
        this.imagePoolPath = imagePoolPath;
    }

    @Override
    public List<PesiPathInfo> queryPesiPaths(String patientName, String seriesDescription) {
        if (patientName == null || patientName.trim().isEmpty()) {
            throw new IllegalArgumentException("患者姓名不能为空");
        }

        LOG.info(String.format("查询PESI路径: 患者=%s, 序列描述=%s", patientName, seriesDescription));

        // 1. 构建SQL查询
        String sql = buildQuerySql(patientName, seriesDescription);

        // 2. 执行数据库查询
        List<PesiQueryResult> queryResults = executeQuery(sql);

        // 3. 为每个查询结果构建PESI路径
        List<PesiPathInfo> pathInfos = new ArrayList<>();
        for (PesiQueryResult result : queryResults) {
            try {
                String actualPath = buildPesiPath(result);
                if (actualPath != null) {
                    boolean exists = validatePesiPath(actualPath);
                    long fileSize = exists ? getFileSize(actualPath) : 0L;
                    pathInfos.add(new PesiPathInfo(actualPath, result, exists, fileSize));
                }
            } catch (Exception e) {
                LOG.warning(String.format("构建PESI路径失败: %s - %s", result.toString(), e.getMessage()));
            }
        }

        LOG.info(String.format("PESI路径查询完成: 找到 %d 个有效路径", pathInfos.size()));
        return pathInfos;
    }

    @Override
    public List<PesiQueryResult> executeQuery(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return Collections.emptyList();
        }

        LOG.info("执行PESI SQL查询");

        // 执行命令
        SystemCommandService.CommandResult result = commandService.executeQuery(sql);

        if (!result.isSuccess()) {
            LOG.warning("PESI查询失败: " + result.getErrorMessage());
            return Collections.emptyList();
        }

        // 解析查询结果
        return parseQueryResult(result.getStdout());
    }

    @Override
    public String buildPesiPath(PesiQueryResult result) {
        if (result == null) {
            return null;
        }

        // 构建find命令的搜索模式
        String searchPattern = String.format("%s/p%s/e%s/s%s/i%s.*",
                imagePoolPath,
                result.getPatientId(),
                result.getExamId(),
                result.getImageSetId(),
                result.getImageId());

        LOG.fine("构建PESI搜索模式: " + searchPattern);

        // 执行find命令
        List<String> foundFiles = commandService.findFiles(searchPattern);

        if (foundFiles.isEmpty()) {
            LOG.fine("未找到匹配的PESI文件: " + searchPattern);
            return null;
        }

        // 返回第一个找到的文件路径
        String pesiPath = foundFiles.get(0);
        LOG.fine("找到PESI路径: " + pesiPath);

        return pesiPath;
    }

    @Override
    public boolean validatePesiPath(String pesiPath) {
        if (pesiPath == null || pesiPath.trim().isEmpty()) {
            return false;
        }

        try {
            File file = new File(pesiPath);
            boolean exists = file.exists() && file.isFile() && file.canRead();

            if (!exists) {
                LOG.fine("PESI路径验证失败: " + pesiPath);
            }

            return exists;

        } catch (Exception e) {
            LOG.warning("验证PESI路径时发生错误: " + pesiPath + " - " + e.getMessage());
            return false;
        }
    }

    @Override
    public boolean isServiceAvailable() {
        return commandService.isExecuteQueryAvailable();
    }

    // === 私有方法 ===

    private String buildQuerySql(String patientName, String seriesDescription) {
        StringBuilder sql = new StringBuilder();
        sql.append("select img.patient_id,img.exam_id,img.image_set_id,img.image_id,img.dcm_image_id ");
        sql.append("from v_image img,imageset series, exam study ");
        sql.append("where study.exam_id=series.exam_id ");
        sql.append("and series.image_set_id=img.image_set_id ");

        // 添加患者姓名条件
        sql.append("and study.patient_name_unicode='").append(escapeSqlString(patientName)).append("' ");

        // 添加序列描述条件（如果提供）
        if (seriesDescription != null && !seriesDescription.trim().isEmpty()) {
            sql.append("and series.series_description='").append(escapeSqlString(seriesDescription)).append("' ");
        }

        // 添加排序和限制
        sql.append("order by img.patient_id, img.exam_id, img.image_set_id, img.image_id ");
        sql.append("limit 1000;"); // 限制结果数量

        String finalSql = sql.toString();
        LOG.fine("构建的SQL查询: " + finalSql);

        return finalSql;
    }

    private String escapeSqlString(String input) {
        if (input == null) {
            return "";
        }
        // 简单的SQL转义，实际应用中可能需要更复杂的转义逻辑
        return input.replace("'", "''");
    }

    private List<PesiQueryResult> parseQueryResult(String output) {
        List<PesiQueryResult> results = new ArrayList<>();

        if (output == null || output.trim().isEmpty()) {
            return results;
        }

        // 使用SystemCommandServiceImpl的解析方法
        if (commandService instanceof SystemCommandServiceImpl) {
            SystemCommandServiceImpl impl = (SystemCommandServiceImpl) commandService;
            List<Map<String, String>> rows = impl.parseQueryOutput(output);

            for (Map<String, String> row : rows) {
                try {
                    PesiQueryResult result = new PesiQueryResult();
                    result.setPatientId(row.get("patient_id"));
                    result.setExamId(row.get("exam_id"));
                    result.setImageSetId(row.get("image_set_id"));
                    result.setImageId(row.get("image_id"));
                    result.setDcmImageId(row.get("dcm_image_id"));

                    // 验证必要字段
                    if (result.getPatientId() != null && result.getExamId() != null &&
                        result.getImageSetId() != null && result.getImageId() != null) {
                        results.add(result);
                    }

                } catch (Exception e) {
                    LOG.warning("解析查询结果行失败: " + row.toString() + " - " + e.getMessage());
                }
            }
        } else {
            // 备用解析方法
            results = parseQueryResultFallback(output);
        }

        LOG.info(String.format("解析PESI查询结果: %d 条记录", results.size()));
        return results;
    }

    private List<PesiQueryResult> parseQueryResultFallback(String output) {
        List<PesiQueryResult> results = new ArrayList<>();

        String[] lines = output.split("\n");
        boolean inDataSection = false;

        for (String line : lines) {
            line = line.trim();

            // 跳过警告和空行
            if (line.startsWith("warning:") || line.isEmpty()) {
                continue;
            }

            // 检查是否进入数据区域
            if (line.matches("^-+\\+-+.*")) {
                inDataSection = true;
                continue;
            }

            // 解析数据行
            if (inDataSection && line.contains("|") && !line.startsWith("(")) {
                try {
                    String[] parts = line.split("\\|");
                    if (parts.length >= 5) {
                        PesiQueryResult result = new PesiQueryResult();
                        result.setPatientId(parts[0].trim());
                        result.setExamId(parts[1].trim());
                        result.setImageSetId(parts[2].trim());
                        result.setImageId(parts[3].trim());
                        result.setDcmImageId(parts[4].trim());

                        results.add(result);
                    }
                } catch (Exception e) {
                    LOG.warning("解析数据行失败: " + line + " - " + e.getMessage());
                }
            }

            // 检查是否到达结果统计行
            if (line.startsWith("(") && line.endsWith("rows)")) {
                break;
            }
        }

        return results;
    }

    private long getFileSize(String filePath) {
        try {
            File file = new File(filePath);
            return file.exists() ? file.length() : 0L;
        } catch (Exception e) {
            return 0L;
        }
    }
}
