package com.ge.med.ct.dcm.table;

import com.ge.med.ct.dcm.tag.DicomTagConstants;

/**
 * TagFormatter测试类
 * 验证标签格式化功能是否正常工作
 */
public class TagFormatterTest {
    
    public static void main(String[] args) {
        try {
            testBasicFormatting();
            testSpecialFormatting();
            testTableFormatting();
            testUtilityMethods();
            System.out.println("✅ TagFormatter测试通过！");
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testBasicFormatting() {
        System.out.println("测试基本格式化...");
        
        TagFormatter formatter = new TagFormatter();
        
        // 测试整数格式化
        String formattedInt = formatter.formatInteger(512);
        assert "512".equals(formattedInt) : "整数格式化错误: " + formattedInt;
        
        // 测试浮点数格式化
        String formattedFloat = formatter.formatFloat(1.5);
        assert "1.5".equals(formattedFloat) : "浮点数格式化错误: " + formattedFloat;
        
        // 测试日期格式化
        String formattedDate = formatter.formatDate("20231201");
        assert "2023-12-01".equals(formattedDate) : "日期格式化错误: " + formattedDate;
        
        // 测试时间格式化
        String formattedTime = formatter.formatTime("120000");
        assert "12:00:00".equals(formattedTime) : "时间格式化错误: " + formattedTime;
        
        // 测试患者姓名格式化
        String formattedName = formatter.formatPatientName("Smith^John^Middle");
        assert "Smith, John Middle".equals(formattedName) : "患者姓名格式化错误: " + formattedName;
        
        // 测试年龄格式化
        String formattedAge = formatter.formatAge("025Y");
        assert "25 岁".equals(formattedAge) : "年龄格式化错误: " + formattedAge;
        
        // 测试性别格式化
        String formattedSex = formatter.formatSex("M");
        assert "男".equals(formattedSex) : "性别格式化错误: " + formattedSex;
        
        // 测试模态格式化
        String formattedModality = formatter.formatModality("CT");
        assert "CT".equals(formattedModality) : "模态格式化错误: " + formattedModality;
        
        System.out.println("✓ 基本格式化测试通过");
    }
    
    private static void testSpecialFormatting() {
        System.out.println("\n测试特殊格式化...");
        
        TagFormatter formatter = new TagFormatter();
        
        // 测试图像位置格式化
        String formattedPosition = formatter.formatImagePosition("-100.5\\200.0\\-50.25");
        assert "(-100.5, 200.0, -50.3)".equals(formattedPosition) : "图像位置格式化错误: " + formattedPosition;
        
        // 测试像素间距格式化
        String formattedSpacing = formatter.formatPixelSpacing("0.5\\0.5");
        assert "0.5 x 0.5 mm".equals(formattedSpacing) : "像素间距格式化错误: " + formattedSpacing;
        
        // 测试窗位窗宽格式化
        String formattedWindow = formatter.formatWindowValue("400\\800");
        assert "400".equals(formattedWindow) : "窗位窗宽格式化错误: " + formattedWindow;
        
        // 测试层厚格式化
        String formattedThickness = formatter.formatSliceThickness("5.0");
        assert "5 mm".equals(formattedThickness) : "层厚格式化错误: " + formattedThickness;
        
        // 测试层位置格式化
        String formattedLocation = formatter.formatSliceLocation("-25.5");
        assert "-25.5 mm".equals(formattedLocation) : "层位置格式化错误: " + formattedLocation;
        
        // 测试KVP格式化
        String formattedKVP = formatter.formatKVP("120");
        assert "120 kV".equals(formattedKVP) : "KVP格式化错误: " + formattedKVP;
        
        // 测试曝光时间格式化
        String formattedExposure = formatter.formatExposureTime("100");
        assert "100 ms".equals(formattedExposure) : "曝光时间格式化错误: " + formattedExposure;
        
        // 测试管电流格式化
        String formattedCurrent = formatter.formatTubeCurrent("200");
        assert "200 mA".equals(formattedCurrent) : "管电流格式化错误: " + formattedCurrent;
        
        // 测试体重格式化
        String formattedWeight = formatter.formatWeight("70.5");
        assert "70.5 kg".equals(formattedWeight) : "体重格式化错误: " + formattedWeight;
        
        // 测试身高格式化
        String formattedHeight = formatter.formatHeight("1.75");
        assert "1.75 m".equals(formattedHeight) : "身高格式化错误: " + formattedHeight;
        
        System.out.println("✓ 特殊格式化测试通过");
    }
    
    private static void testTableFormatting() {
        System.out.println("\n测试表格格式化...");
        
        TagFormatter formatter = new TagFormatter();
        
        // 测试使用DicomTagConstants的格式化
        String formattedPatientId = formatter.format(DicomTagConstants.Patient.PATIENT_ID, "PATIENT001");
        assert "PATIENT001".equals(formattedPatientId) : "患者ID格式化错误: " + formattedPatientId;
        
        String formattedRows = formatter.format(DicomTagConstants.Image.ROWS, 512);
        assert "512".equals(formattedRows) : "行数格式化错误: " + formattedRows;
        
        String formattedPixelSpacing = formatter.format(DicomTagConstants.Image.PIXEL_SPACING, "0.5\\0.5");
        assert "0.5 x 0.5 mm".equals(formattedPixelSpacing) : "像素间距表格格式化错误: " + formattedPixelSpacing;
        
        // 测试formatForDisplay方法
        String displayPatientName = formatter.formatForDisplay(DicomTagConstants.Patient.PATIENT_NAME, "Smith^John");
        assert "Smith, John".equals(displayPatientName) : "患者姓名显示格式化错误: " + displayPatientName;
        
        String displayModality = formatter.formatForDisplay(DicomTagConstants.Series.MODALITY, "CT");
        assert "CT".equals(displayModality) : "模态显示格式化错误: " + displayModality;
        
        System.out.println("✓ 表格格式化测试通过");
    }
    
    private static void testUtilityMethods() {
        System.out.println("\n测试工具方法...");
        
        TagFormatter formatter = new TagFormatter();
        
        // 测试文本截断
        String longText = "这是一个非常长的文本，需要被截断以适应表格显示";
        String truncated = formatter.truncateText(longText, 20);
        assert truncated.length() <= 20 : "文本截断长度错误: " + truncated.length();
        assert truncated.endsWith("...") : "截断文本应以...结尾: " + truncated;
        
        // 测试安全文本格式化
        String unsafeText = "包含\n换行符\t和制表符的文本";
        String safeText = formatter.formatSafeText(unsafeText);
        assert !safeText.contains("\n") : "安全文本不应包含换行符: " + safeText;
        assert !safeText.contains("\t") : "安全文本不应包含制表符: " + safeText;
        
        // 测试数值范围格式化
        String valueRange = formatter.formatValueRange("100\\200\\300\\400\\500");
        assert valueRange.contains("100, 200, 300") : "数值范围格式化错误: " + valueRange;
        assert valueRange.contains("...") : "数值范围应包含省略号: " + valueRange;
        
        // 测试特殊格式化检查
        assert formatter.needsSpecialFormatting(DicomTagConstants.Image.IMAGE_POSITION_PATIENT) : "图像位置应需要特殊格式化";
        assert formatter.needsSpecialFormatting(DicomTagConstants.Image.PIXEL_SPACING) : "像素间距应需要特殊格式化";
        assert formatter.needsSpecialFormatting(DicomTagConstants.CT.KVP) : "KVP应需要特殊格式化";
        assert !formatter.needsSpecialFormatting(DicomTagConstants.Patient.PATIENT_ID) : "患者ID不应需要特殊格式化";
        
        // 测试null值处理
        assert "".equals(formatter.format(DicomTagConstants.Patient.PATIENT_ID, null)) : "null值应返回空字符串";
        assert "".equals(formatter.formatInteger(null)) : "null整数应返回空字符串";
        assert "".equals(formatter.formatFloat(null)) : "null浮点数应返回空字符串";
        
        System.out.println("✓ 工具方法测试通过");
    }
}
