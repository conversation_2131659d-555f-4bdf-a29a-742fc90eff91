package com.ge.med.ct.dcm.table;

import com.ge.med.ct.dcm.model.DicomExam;
import com.ge.med.ct.dcm.model.DicomSeries;
import com.ge.med.ct.dcm.model.DicomImage;
import com.ge.med.ct.dcm.model.DicomTag;
import com.ge.med.ct.dcm.tag.DicomTagConstants;
import com.ge.med.ct.dcm.table.TableDataConverter.TableType;
import com.ge.med.ct.exception.core.DicomException;
import org.dcm4che3.data.VR;

import java.util.Arrays;
import java.util.List;
import java.util.Vector;

/**
 * TableDataConverter测试类
 * 验证重构后的表格数据转换功能
 */
public class TableDataConverterTest {
    
    public static void main(String[] args) {
        try {
            testTableDataConverter();
            testBackwardCompatibility();
            System.out.println("✅ TableDataConverter重构测试通过！");
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testTableDataConverter() throws DicomException {
        System.out.println("测试新的TableDataConverter...");
        
        TableDataConverter converter = new TableDataConverter();
        
        // 创建测试数据
        DicomExam exam = createTestExam();
        DicomSeries series = createTestSeries();
        DicomImage image = createTestImage();
        
        // 建立关联
        exam.addSeries(series);
        series.addImage(image);
        
        // 测试检查表格转换
        List<List<String>> examTable = converter.convertToTable(Arrays.asList(exam), TableType.EXAM);
        List<String> examColumns = converter.getColumnNames(TableType.EXAM);
        
        assert !examTable.isEmpty() : "检查表格不应为空";
        assert !examColumns.isEmpty() : "检查列名不应为空";
        assert examTable.get(0).size() == examColumns.size() : "检查表格列数应匹配";
        
        System.out.println("✓ 检查表格: " + examColumns.size() + " 列, " + examTable.size() + " 行");
        
        // 测试序列表格转换
        List<List<String>> seriesTable = converter.convertToTable(Arrays.asList(series), TableType.SERIES);
        List<String> seriesColumns = converter.getColumnNames(TableType.SERIES);
        
        assert !seriesTable.isEmpty() : "序列表格不应为空";
        assert !seriesColumns.isEmpty() : "序列列名不应为空";
        assert seriesTable.get(0).size() == seriesColumns.size() : "序列表格列数应匹配";
        
        System.out.println("✓ 序列表格: " + seriesColumns.size() + " 列, " + seriesTable.size() + " 行");
        
        // 测试图像表格转换
        List<List<String>> imageTable = converter.convertToTable(Arrays.asList(image), TableType.IMAGE);
        List<String> imageColumns = converter.getColumnNames(TableType.IMAGE);
        
        assert !imageTable.isEmpty() : "图像表格不应为空";
        assert !imageColumns.isEmpty() : "图像列名不应为空";
        assert imageTable.get(0).size() == imageColumns.size() : "图像表格列数应匹配";
        
        System.out.println("✓ 图像表格: " + imageColumns.size() + " 列, " + imageTable.size() + " 行");
        
        // 测试Vector兼容性
        Vector<Vector<String>> examVector = converter.convertToVector(Arrays.asList(exam), TableType.EXAM);
        String[] examColumnArray = converter.getColumnNamesArray(TableType.EXAM);
        
        assert !examVector.isEmpty() : "检查Vector不应为空";
        assert examColumnArray.length > 0 : "检查列名数组不应为空";
        
        System.out.println("✓ Vector兼容性测试通过");
        
        // 验证数据内容
        List<String> examRow = examTable.get(0);
        int patientIdIndex = examColumns.indexOf("PatientID");
        int patientNameIndex = examColumns.indexOf("PatientName");
        
        if (patientIdIndex >= 0) {
            assert "PATIENT001".equals(examRow.get(patientIdIndex)) : "患者ID应正确格式化";
        }
        
        if (patientNameIndex >= 0) {
            assert "Smith, John".equals(examRow.get(patientNameIndex)) : "患者姓名应正确格式化";
        }
        
        System.out.println("✓ 数据格式化验证通过");
    }
    
    private static void testBackwardCompatibility() throws DicomException {
        System.out.println("\n测试向后兼容性...");
        
        // 使用适配器测试向后兼容
        com.ge.med.ct.service.TableDataEngineAdapter adapter = 
            new com.ge.med.ct.service.TableDataEngineAdapter();
        
        // 创建测试数据
        DicomExam exam = createTestExam();
        DicomSeries series = createTestSeries();
        DicomImage image = createTestImage();
        
        // 测试旧API
        Vector<Vector<String>> examData = adapter.convertExamData(Arrays.asList(exam));
        Vector<Vector<String>> seriesData = adapter.convertSeriesData(Arrays.asList(series));
        Vector<Vector<String>> imageData = adapter.convertImageData(Arrays.asList(image));
        
        assert !examData.isEmpty() : "检查数据不应为空";
        assert !seriesData.isEmpty() : "序列数据不应为空";
        assert !imageData.isEmpty() : "图像数据不应为空";
        
        // 测试列名获取
        String[] examColumns = adapter.getTableColumnNames("TABLE_EXAM");
        String[] seriesColumns = adapter.getTableColumnNames("TABLE_SERIES");
        String[] imageColumns = adapter.getTableColumnNames("TABLE_IMAGE");
        
        assert examColumns.length > 0 : "检查列名不应为空";
        assert seriesColumns.length > 0 : "序列列名不应为空";
        assert imageColumns.length > 0 : "图像列名不应为空";
        
        System.out.println("✓ 向后兼容性测试通过");
    }
    
    private static DicomExam createTestExam() throws DicomException {
        DicomExam exam = new DicomExam("EXAM001");
        
        // 添加患者信息
        exam.addTag(DicomTagConstants.Patient.PATIENT_ID, 
                   new DicomTag(DicomTagConstants.Patient.PATIENT_ID, "PATIENT001", VR.LO));
        exam.addTag(DicomTagConstants.Patient.PATIENT_NAME, 
                   new DicomTag(DicomTagConstants.Patient.PATIENT_NAME, "Smith^John", VR.PN));
        exam.addTag(DicomTagConstants.Patient.PATIENT_SEX, 
                   new DicomTag(DicomTagConstants.Patient.PATIENT_SEX, "M", VR.CS));
        exam.addTag(DicomTagConstants.Patient.PATIENT_AGE, 
                   new DicomTag(DicomTagConstants.Patient.PATIENT_AGE, "025Y", VR.AS));
        
        // 添加检查信息
        exam.addTag(DicomTagConstants.Study.STUDY_INSTANCE_UID, 
                   new DicomTag(DicomTagConstants.Study.STUDY_INSTANCE_UID, "*******.5", VR.UI));
        exam.addTag(DicomTagConstants.Study.STUDY_DATE, 
                   new DicomTag(DicomTagConstants.Study.STUDY_DATE, "20231201", VR.DA));
        exam.addTag(DicomTagConstants.Study.STUDY_TIME, 
                   new DicomTag(DicomTagConstants.Study.STUDY_TIME, "120000", VR.TM));
        exam.addTag(DicomTagConstants.Study.STUDY_DESCRIPTION, 
                   new DicomTag(DicomTagConstants.Study.STUDY_DESCRIPTION, "Test Study", VR.LO));
        
        return exam;
    }
    
    private static DicomSeries createTestSeries() throws DicomException {
        DicomSeries series = new DicomSeries("SERIES001");
        
        // 添加序列信息
        series.addTag(DicomTagConstants.Series.SERIES_INSTANCE_UID, 
                     new DicomTag(DicomTagConstants.Series.SERIES_INSTANCE_UID, "*******.5.6", VR.UI));
        series.addTag(DicomTagConstants.Series.SERIES_NUMBER, 
                     new DicomTag(DicomTagConstants.Series.SERIES_NUMBER, "1", VR.IS));
        series.addTag(DicomTagConstants.Series.SERIES_DESCRIPTION, 
                     new DicomTag(DicomTagConstants.Series.SERIES_DESCRIPTION, "Test Series", VR.LO));
        series.addTag(DicomTagConstants.Series.MODALITY, 
                     new DicomTag(DicomTagConstants.Series.MODALITY, "CT", VR.CS));
        series.addTag(DicomTagConstants.Series.SERIES_DATE, 
                     new DicomTag(DicomTagConstants.Series.SERIES_DATE, "20231201", VR.DA));
        series.addTag(DicomTagConstants.Series.SERIES_TIME, 
                     new DicomTag(DicomTagConstants.Series.SERIES_TIME, "120000", VR.TM));
        
        return series;
    }
    
    private static DicomImage createTestImage() throws DicomException {
        DicomImage image = new DicomImage("IMAGE001");
        
        // 添加图像信息
        image.addTag(DicomTagConstants.Image.SOP_INSTANCE_UID, 
                    new DicomTag(DicomTagConstants.Image.SOP_INSTANCE_UID, "*******.5.6.7", VR.UI));
        image.addTag(DicomTagConstants.Image.INSTANCE_NUMBER, 
                    new DicomTag(DicomTagConstants.Image.INSTANCE_NUMBER, "1", VR.IS));
        image.addTag(DicomTagConstants.Image.IMAGE_TYPE, 
                    new DicomTag(DicomTagConstants.Image.IMAGE_TYPE, "ORIGINAL\\PRIMARY\\AXIAL", VR.CS));
        image.addTag(DicomTagConstants.Image.ROWS, 
                    new DicomTag(DicomTagConstants.Image.ROWS, "512", VR.US));
        image.addTag(DicomTagConstants.Image.COLUMNS, 
                    new DicomTag(DicomTagConstants.Image.COLUMNS, "512", VR.US));
        image.addTag(DicomTagConstants.Image.IMAGE_POSITION_PATIENT, 
                    new DicomTag(DicomTagConstants.Image.IMAGE_POSITION_PATIENT, "-100.5\\200.0\\-50.25", VR.DS));
        image.addTag(DicomTagConstants.Image.SLICE_LOCATION, 
                    new DicomTag(DicomTagConstants.Image.SLICE_LOCATION, "-25.5", VR.DS));
        image.addTag(DicomTagConstants.Image.SLICE_THICKNESS, 
                    new DicomTag(DicomTagConstants.Image.SLICE_THICKNESS, "5.0", VR.DS));
        image.addTag(DicomTagConstants.Image.PIXEL_SPACING, 
                    new DicomTag(DicomTagConstants.Image.PIXEL_SPACING, "0.5\\0.5", VR.DS));
        image.addTag(DicomTagConstants.Image.WINDOW_CENTER, 
                    new DicomTag(DicomTagConstants.Image.WINDOW_CENTER, "400", VR.DS));
        image.addTag(DicomTagConstants.Image.WINDOW_WIDTH, 
                    new DicomTag(DicomTagConstants.Image.WINDOW_WIDTH, "800", VR.DS));
        
        return image;
    }
}
