package com.ge.med.ct.dcm.model;

import com.ge.med.ct.exception.core.DicomException;
import org.dcm4che3.data.VR;

/**
 * 循环调用测试类
 * 验证DicomExam、DicomSeries、DicomImage之间的循环调用问题是否已修复
 */
public class CircularCallTest {
    
    public static void main(String[] args) {
        try {
            testBasicAssociations();
            testComplexAssociations();
            testReassociations();
            testRemovalOperations();
            System.out.println("✅ 所有循环调用测试通过！");
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testBasicAssociations() throws DicomException {
        System.out.println("测试基本关联操作...");
        
        // 创建对象
        DicomExam exam = new DicomExam("EXAM001");
        DicomSeries series = new DicomSeries("SERIES001");
        DicomImage image = new DicomImage("IMAGE001");
        
        // 测试 exam -> series 关联
        exam.addSeries(series);
        assert series.getExam() == exam : "序列应关联到检查";
        assert exam.getSeries().contains(series) : "检查应包含序列";
        
        // 测试 series -> image 关联
        series.addImage(image);
        assert image.getSeries() == series : "图像应关联到序列";
        assert series.getImages().contains(image) : "序列应包含图像";
        
        System.out.println("✓ 基本关联操作正常");
    }
    
    private static void testComplexAssociations() throws DicomException {
        System.out.println("\n测试复杂关联操作...");
        
        DicomExam exam1 = new DicomExam("EXAM001");
        DicomExam exam2 = new DicomExam("EXAM002");
        DicomSeries series1 = new DicomSeries("SERIES001");
        DicomSeries series2 = new DicomSeries("SERIES002");
        DicomImage image1 = new DicomImage("IMAGE001");
        DicomImage image2 = new DicomImage("IMAGE002");
        
        // 建立初始关联
        exam1.addSeries(series1);
        series1.addImage(image1);
        
        // 测试序列重新关联到不同检查
        exam2.addSeries(series1);
        assert series1.getExam() == exam2 : "序列应重新关联到新检查";
        assert !exam1.getSeries().contains(series1) : "旧检查不应包含序列";
        assert exam2.getSeries().contains(series1) : "新检查应包含序列";
        
        // 测试图像重新关联到不同序列
        series2.addImage(image1);
        assert image1.getSeries() == series2 : "图像应重新关联到新序列";
        assert !series1.getImages().contains(image1) : "旧序列不应包含图像";
        assert series2.getImages().contains(image1) : "新序列应包含图像";
        
        System.out.println("✓ 复杂关联操作正常");
    }
    
    private static void testReassociations() throws DicomException {
        System.out.println("\n测试重新关联操作...");
        
        DicomExam exam = new DicomExam("EXAM001");
        DicomSeries series = new DicomSeries("SERIES001");
        DicomImage image = new DicomImage("IMAGE001");
        
        // 建立关联
        exam.addSeries(series);
        series.addImage(image);
        
        // 测试使用setter方法重新关联
        DicomExam newExam = new DicomExam("EXAM002");
        series.setExam(newExam);
        
        assert series.getExam() == newExam : "序列应关联到新检查";
        assert !exam.getSeries().contains(series) : "旧检查不应包含序列";
        assert newExam.getSeries().contains(series) : "新检查应包含序列";
        
        // 测试图像重新关联
        DicomSeries newSeries = new DicomSeries("SERIES002");
        image.setSeries(newSeries);
        
        assert image.getSeries() == newSeries : "图像应关联到新序列";
        assert !series.getImages().contains(image) : "旧序列不应包含图像";
        assert newSeries.getImages().contains(image) : "新序列应包含图像";
        
        System.out.println("✓ 重新关联操作正常");
    }
    
    private static void testRemovalOperations() throws DicomException {
        System.out.println("\n测试移除操作...");
        
        DicomExam exam = new DicomExam("EXAM001");
        DicomSeries series = new DicomSeries("SERIES001");
        DicomImage image = new DicomImage("IMAGE001");
        
        // 建立关联
        exam.addSeries(series);
        series.addImage(image);
        
        // 测试移除图像
        series.removeImage(image);
        assert image.getSeries() == null : "图像不应关联到任何序列";
        assert !series.getImages().contains(image) : "序列不应包含图像";
        
        // 测试移除序列
        exam.removeSeries(series);
        assert series.getExam() == null : "序列不应关联到任何检查";
        assert !exam.getSeries().contains(series) : "检查不应包含序列";
        
        // 测试设置为null
        DicomSeries newSeries = new DicomSeries("SERIES002");
        DicomImage newImage = new DicomImage("IMAGE002");
        
        newSeries.addImage(newImage);
        newImage.setSeries(null);
        
        assert newImage.getSeries() == null : "图像不应关联到任何序列";
        assert !newSeries.getImages().contains(newImage) : "序列不应包含图像";
        
        System.out.println("✓ 移除操作正常");
    }
}
