package com.ge.med.ct.dcm.core;

/**
 * DICOM数据统计信息
 */
public class DicomDataStats {
    private final int examCount;
    private final int seriesCount;
    private final int imageCount;
    private final int fileModelCount;
    private final DataSourceType dataSourceType;
    private final String dataSourceDescription;
    
    public DicomDataStats(int examCount, int seriesCount, int imageCount, int fileModelCount,
                         DataSourceType dataSourceType, String dataSourceDescription) {
        this.examCount = examCount;
        this.seriesCount = seriesCount;
        this.imageCount = imageCount;
        this.fileModelCount = fileModelCount;
        this.dataSourceType = dataSourceType;
        this.dataSourceDescription = dataSourceDescription;
    }
    
    public int getExamCount() {
        return examCount;
    }
    
    public int getSeriesCount() {
        return seriesCount;
    }
    
    public int getImageCount() {
        return imageCount;
    }
    
    public int getFileModelCount() {
        return fileModelCount;
    }
    
    public DataSourceType getDataSourceType() {
        return dataSourceType;
    }
    
    public String getDataSourceDescription() {
        return dataSourceDescription;
    }
    
    @Override
    public String toString() {
        return String.format("DicomDataStats{examCount=%d, seriesCount=%d, imageCount=%d, fileModelCount=%d, dataSourceType=%s}",
                examCount, seriesCount, imageCount, fileModelCount, dataSourceType);
    }
}
