package com.ge.med.ct.dcm.core;

import java.util.List;

/**
 * 系统命令执行服务接口
 * 封装executequery.sh和find命令的执行
 */
public interface SystemCommandService {
    
    /**
     * 执行executequery.sh命令
     * @param sql SQL查询语句
     * @return 命令执行结果
     */
    CommandResult executeQuery(String sql);
    
    /**
     * 执行executequery.sh命令（带参数）
     * @param patientNameUnicode 患者姓名Unicode
     * @param seriesDescription 序列描述
     * @return 命令执行结果
     */
    CommandResult executeQuery(String patientNameUnicode, String seriesDescription);
    
    /**
     * 执行find命令查找文件
     * @param searchPattern 搜索模式
     * @return 找到的文件路径列表
     */
    List<String> findFiles(String searchPattern);
    
    /**
     * 执行find命令查找文件（指定目录）
     * @param directory 搜索目录
     * @param pattern 文件模式
     * @return 找到的文件路径列表
     */
    List<String> findFiles(String directory, String pattern);
    
    /**
     * 检查executequery.sh命令是否可用
     * @return executequery.sh是否可用
     */
    boolean isExecuteQueryAvailable();
    
    /**
     * 设置命令执行超时时间
     * @param timeoutSeconds 超时时间（秒）
     */
    void setCommandTimeout(int timeoutSeconds);
    
    /**
     * 执行通用系统命令
     * @param command 命令数组
     * @param timeoutSeconds 超时时间（秒）
     * @return 命令执行结果
     */
    CommandResult executeCommand(String[] command, int timeoutSeconds);
    
    /**
     * 命令执行结果
     */
    class CommandResult {
        private final int exitCode;
        private final String stdout;
        private final String stderr;
        private final boolean success;
        private final long executionTime;
        private final boolean timedOut;
        
        public CommandResult(int exitCode, String stdout, String stderr, boolean success, long executionTime) {
            this(exitCode, stdout, stderr, success, executionTime, false);
        }
        
        public CommandResult(int exitCode, String stdout, String stderr, boolean success, long executionTime, boolean timedOut) {
            this.exitCode = exitCode;
            this.stdout = stdout != null ? stdout : "";
            this.stderr = stderr != null ? stderr : "";
            this.success = success;
            this.executionTime = executionTime;
            this.timedOut = timedOut;
        }
        
        public int getExitCode() {
            return exitCode;
        }
        
        public String getStdout() {
            return stdout;
        }
        
        public String getStderr() {
            return stderr;
        }
        
        public boolean isSuccess() {
            return success && !timedOut;
        }
        
        public long getExecutionTime() {
            return executionTime;
        }
        
        public boolean isTimedOut() {
            return timedOut;
        }
        
        /**
         * 获取输出行列表
         */
        public List<String> getOutputLines() {
            if (stdout == null || stdout.trim().isEmpty()) {
                return java.util.Collections.emptyList();
            }
            return java.util.Arrays.asList(stdout.split("\n"));
        }
        
        /**
         * 获取错误行列表
         */
        public List<String> getErrorLines() {
            if (stderr == null || stderr.trim().isEmpty()) {
                return java.util.Collections.emptyList();
            }
            return java.util.Arrays.asList(stderr.split("\n"));
        }
        
        /**
         * 检查是否有错误输出
         */
        public boolean hasError() {
            return !success || timedOut || (stderr != null && !stderr.trim().isEmpty());
        }
        
        /**
         * 获取完整的错误信息
         */
        public String getErrorMessage() {
            if (success && !timedOut && (stderr == null || stderr.trim().isEmpty())) {
                return null;
            }
            
            StringBuilder error = new StringBuilder();
            if (timedOut) {
                error.append("命令执行超时");
            } else if (!success) {
                error.append("命令执行失败 (退出码: ").append(exitCode).append(")");
            }
            
            if (stderr != null && !stderr.trim().isEmpty()) {
                if (error.length() > 0) {
                    error.append(": ");
                }
                error.append(stderr);
            }
            return error.toString();
        }
        
        @Override
        public String toString() {
            return String.format("CommandResult{exitCode=%d, success=%s, timedOut=%s, executionTime=%dms, stdoutLines=%d, stderrLength=%d}",
                    exitCode, success, timedOut, executionTime, getOutputLines().size(), stderr.length());
        }
    }
}
