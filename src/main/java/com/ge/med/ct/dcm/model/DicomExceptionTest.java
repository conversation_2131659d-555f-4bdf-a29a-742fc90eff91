package com.ge.med.ct.dcm.model;

import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.message.DicomMessages;
import org.dcm4che3.data.VR;

/**
 * 简单的DicomException测试类
 * 验证异常调用是否正常工作
 */
public class DicomExceptionTest {
    
    public static void main(String[] args) {
        try {
            testDicomExceptionWithMessages();
            testDicomExceptionWithStrings();
            testModelCreation();
            System.out.println("✅ 所有DicomException测试通过！");
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testDicomExceptionWithMessages() throws DicomException {
        System.out.println("测试使用DicomMessages的异常...");
        
        try {
            new DicomExam(null);
            throw new RuntimeException("应该抛出异常");
        } catch (DicomException e) {
            System.out.println("✓ DicomExam异常: " + e.getMessage());
        }
        
        try {
            new DicomSeries("");
            throw new RuntimeException("应该抛出异常");
        } catch (DicomException e) {
            System.out.println("✓ DicomSeries异常: " + e.getMessage());
        }
        
        try {
            new DicomImage("   ");
            throw new RuntimeException("应该抛出异常");
        } catch (DicomException e) {
            System.out.println("✓ DicomImage异常: " + e.getMessage());
        }
        
        try {
            new DicomTag(null, "test", VR.LO);
            throw new RuntimeException("应该抛出异常");
        } catch (DicomException e) {
            System.out.println("✓ DicomTag异常: " + e.getMessage());
        }
        
        try {
            new DicomFileModel("");
            throw new RuntimeException("应该抛出异常");
        } catch (DicomException e) {
            System.out.println("✓ DicomFileModel异常: " + e.getMessage());
        }
    }
    
    private static void testDicomExceptionWithStrings() throws DicomException {
        System.out.println("\n测试使用String的异常...");
        
        try {
            throw new DicomException("这是一个字符串异常消息");
        } catch (DicomException e) {
            System.out.println("✓ String异常: " + e.getMessage());
        }
        
        try {
            throw new DicomException("带参数的异常: {0}", new RuntimeException("原因"));
        } catch (DicomException e) {
            System.out.println("✓ String异常带原因: " + e.getMessage());
        }
    }
    
    private static void testModelCreation() throws DicomException {
        System.out.println("\n测试正常模型创建...");
        
        // 创建正常的模型对象
        DicomExam exam = new DicomExam("EXAM001");
        DicomSeries series = new DicomSeries("SERIES001");
        DicomImage image = new DicomImage("IMAGE001");
        DicomTag tag = new DicomTag("(0010,0020)", "PATIENT001", VR.LO);
        DicomFileModel fileModel = new DicomFileModel("FILE001");
        
        System.out.println("✓ 创建DicomExam: " + exam.getId());
        System.out.println("✓ 创建DicomSeries: " + series.getId());
        System.out.println("✓ 创建DicomImage: " + image.getId());
        System.out.println("✓ 创建DicomTag: " + tag.getTagId());
        System.out.println("✓ 创建DicomFileModel: " + fileModel.getId());
        
        // 测试关联关系
        exam.addSeries(series);
        series.addImage(image);
        
        System.out.println("✓ 建立关联关系成功");
        System.out.println("  - 检查包含序列数: " + exam.getSeriesCount());
        System.out.println("  - 序列包含图像数: " + series.getImageCount());
    }
}
